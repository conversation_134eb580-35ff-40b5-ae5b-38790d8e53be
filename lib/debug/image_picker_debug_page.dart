import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';

/// 图片选择调试页面
class ImagePickerDebugPage extends StatefulWidget {
  const ImagePickerDebugPage({super.key});

  @override
  State<ImagePickerDebugPage> createState() => _ImagePickerDebugPageState();
}

class _ImagePickerDebugPageState extends State<ImagePickerDebugPage> {
  File? _selectedImage;
  String _debugLog = '';

  void _addLog(String message) {
    setState(() {
      _debugLog += '${DateTime.now().toString().substring(11, 19)}: $message\n';
    });
    debugPrint('ImagePickerDebug: $message');
  }

  /// 检查权限状态
  Future<void> _checkPermissionStatus() async {
    _addLog('开始检查权限状态...');

    try {
      // 检查相册权限
      final photosStatus = await Permission.photos.status;
      _addLog('相册权限状态: $photosStatus');

      // 检查相机权限
      final cameraStatus = await Permission.camera.status;
      _addLog('相机权限状态: $cameraStatus');

      // 检查存储权限（Android）
      if (Platform.isAndroid) {
        final storageStatus = await Permission.storage.status;
        _addLog('存储权限状态: $storageStatus');

        final mediaImagesStatus = await Permission.photos.status;
        _addLog('媒体图片权限状态: $mediaImagesStatus');
      }
    } catch (e) {
      _addLog('检查权限状态失败: $e');
    }
  }

  /// 请求相册权限
  Future<bool> _requestPhotosPermission() async {
    _addLog('请求相册权限...');

    try {
      Permission permission = Permission.photos;

      final status = await permission.status;
      _addLog('当前相册权限状态: $status');

      if (status.isGranted) {
        _addLog('相册权限已授予');
        return true;
      } else if (status.isDenied) {
        _addLog('相册权限被拒绝，尝试请求...');
        final result = await permission.request();
        _addLog('权限请求结果: $result');
        return result.isGranted;
      } else if (status.isPermanentlyDenied) {
        _addLog('相册权限被永久拒绝');
        return false;
      }

      return false;
    } catch (e) {
      _addLog('请求相册权限失败: $e');
      return false;
    }
  }

  /// 测试图片选择（不检查权限）
  Future<void> _testPickImageDirect(ImageSource source) async {
    final sourceName = source == ImageSource.camera ? '相机' : '相册';
    _addLog('直接测试$sourceName选择（跳过权限检查）...');

    try {
      final picker = ImagePicker();
      _addLog('创建ImagePicker实例成功');

      _addLog('调用pickImage方法...');
      final pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        _addLog('图片选择成功: ${pickedFile.path}');
        setState(() {
          _selectedImage = File(pickedFile.path);
        });
      } else {
        _addLog('用户取消了图片选择');
      }
    } catch (e) {
      _addLog('图片选择失败: $e');
      _addLog('错误类型: ${e.runtimeType}');
    }
  }

  /// 测试图片选择（带权限检查）
  Future<void> _testPickImageWithPermission(ImageSource source) async {
    final sourceName = source == ImageSource.camera ? '相机' : '相册';
    _addLog('测试$sourceName选择（带权限检查）...');

    try {
      // 检查权限
      bool hasPermission = false;
      if (source == ImageSource.camera) {
        final status = await Permission.camera.status;
        if (status.isGranted) {
          hasPermission = true;
        } else {
          final result = await Permission.camera.request();
          hasPermission = result.isGranted;
        }
      } else {
        hasPermission = await _requestPhotosPermission();
      }

      if (!hasPermission) {
        _addLog('权限未授予，无法继续');
        return;
      }

      _addLog('权限检查通过，开始选择图片...');
      await _testPickImageDirect(source);
    } catch (e) {
      _addLog('带权限检查的图片选择失败: $e');
    }
  }

  /// 测试图片裁剪功能
  Future<void> _testImageCropper() async {
    _addLog('测试图片裁剪功能...');

    if (_selectedImage == null) {
      _addLog('请先选择一张图片');
      return;
    }

    try {
      _addLog('开始裁剪图片: ${_selectedImage!.path}');

      final croppedFile = await ImageCropper().cropImage(
        sourcePath: _selectedImage!.path,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 90,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: '测试裁剪',
            toolbarColor: const Color(0xFF2F76DA),
            toolbarWidgetColor: Colors.white,
            backgroundColor: Colors.white,
            activeControlsWidgetColor: const Color(0xFF2F76DA),
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
            aspectRatioPresets: [CropAspectRatioPreset.square],
            showCropGrid: true,
            cropGridRowCount: 3,
            cropGridColumnCount: 3,
            cropGridColor: Colors.grey,
            cropGridStrokeWidth: 1,
            cropFrameColor: const Color(0xFF2F76DA),
            cropFrameStrokeWidth: 2,
            hideBottomControls: false,
            statusBarColor: const Color(0xFF2F76DA),
          ),
          IOSUiSettings(
            title: '测试裁剪',
            doneButtonTitle: '完成',
            cancelButtonTitle: '取消',
            aspectRatioLockEnabled: true,
            resetAspectRatioEnabled: false,
            aspectRatioPickerButtonHidden: true,
            resetButtonHidden: true,
            rotateButtonsHidden: false,
            aspectRatioPresets: [CropAspectRatioPreset.square],
          ),
        ],
      );

      if (croppedFile != null) {
        _addLog('图片裁剪成功: ${croppedFile.path}');
        setState(() {
          _selectedImage = File(croppedFile.path);
        });
      } else {
        _addLog('用户取消了裁剪');
      }
    } catch (e) {
      _addLog('图片裁剪失败: $e');
      _addLog('错误类型: ${e.runtimeType}');
    }
  }

  /// 清除日志
  void _clearLog() {
    setState(() {
      _debugLog = '';
      _selectedImage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图片选择调试'),
        backgroundColor: const Color(0xFF2F76DA),
        foregroundColor: Colors.white,
        actions: [
          IconButton(onPressed: _clearLog, icon: const Icon(Icons.clear)),
        ],
      ),
      body: Column(
        children: [
          // 选中的图片显示
          if (_selectedImage != null)
            Container(
              height: 200,
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Image.file(_selectedImage!, fit: BoxFit.cover),
            ),

          // 测试按钮
          Padding(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _checkPermissionStatus,
                  child: const Text('检查权限状态'),
                ),
                ElevatedButton(
                  onPressed: _requestPhotosPermission,
                  child: const Text('请求相册权限'),
                ),
                ElevatedButton(
                  onPressed: () => _testPickImageDirect(ImageSource.gallery),
                  child: const Text('直接选择相册'),
                ),
                ElevatedButton(
                  onPressed: () => _testPickImageDirect(ImageSource.camera),
                  child: const Text('直接选择相机'),
                ),
                ElevatedButton(
                  onPressed: () =>
                      _testPickImageWithPermission(ImageSource.gallery),
                  child: const Text('相册（带权限）'),
                ),
                ElevatedButton(
                  onPressed: () =>
                      _testPickImageWithPermission(ImageSource.camera),
                  child: const Text('相机（带权限）'),
                ),
                ElevatedButton(
                  onPressed: _testImageCropper,
                  child: const Text('测试裁剪'),
                ),
              ],
            ),
          ),

          // 调试日志
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _debugLog.isEmpty ? '等待调试信息...' : _debugLog,
                  style: const TextStyle(
                    color: Colors.green,
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
