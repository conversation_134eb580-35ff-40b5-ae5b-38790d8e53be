import 'dart:io';
import 'dart:convert';
import 'dart:async'; // 用于Completer
import 'dart:ui'; // 用于毛玻璃效果
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:vector_math/vector_math_64.dart' show Vector3;

// 导入增强分享服务
import '../../shared/services/enhanced_share_service.dart';
// 导入成就系统
import '../achievement/providers/achievement_provider.dart';
// 导入标准化坐标系统
import 'utils/image_coordinate_system.dart' as coord;
// 导入锚点数据迁移工具
import 'utils/anchor_data_migration.dart';

/// 记忆场景详情页 - AR实景记忆锚点系统
class SceneDetailPage extends ConsumerStatefulWidget {
  final String sceneId;
  final String sceneTitle;
  final String sceneImagePath;
  final List<String>? palaceImagePaths; // 新增：当前宫殿的所有图片
  final Function(String sceneId, int anchorCount)?
  onAnchorCountChanged; // 新增：知识点数量变化回调

  const SceneDetailPage({
    super.key,
    required this.sceneId,
    required this.sceneTitle,
    required this.sceneImagePath,
    this.palaceImagePaths, // 可选参数，用于显示底部预览
    this.onAnchorCountChanged, // 可选参数，用于通知知识点数量变化
  });

  @override
  ConsumerState<SceneDetailPage> createState() => _SceneDetailPageState();
}

class _SceneDetailPageState extends ConsumerState<SceneDetailPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  // 手势冲突解决：用于全屏上滑分享
  final Set<int> _activePointers = {};
  Offset? _dragStartPointerPosition;
  Duration? _dragDownTimestamp;
  bool _isVerticalDragForShare = false;
  bool _forceHideUI = false; // 新增：强制隐藏UI的状态

  // 分享防抖机制 - 防止多个手势系统同时触发分享
  DateTime? _lastShareTime;
  static const Duration _shareDebounceInterval = Duration(milliseconds: 500);

  // 设备感知的坐标系统 - 修复真机与模拟器的坐标差异
  late double _devicePixelRatio;
  late double _coordinateScaleFactor;
  bool _isCoordinateSystemInitialized = false;

  // 相册滑动相关
  late PageController _pageController;
  int _currentPageIndex = 0;
  List<String> _imageList = [];

  // 每个图片的独立变换控制器
  final Map<int, TransformationController> _transformControllers = {};

  bool _isEditMode = false;
  bool _showAllAnchors = true;
  MemoryAnchor? _selectedAnchor;
  bool _showAddKnowledgePanel = false; // 控制添加知识点面板的显示

  // 位置选择模式相关状态
  bool _isPositionSelectionMode = false; // 是否处于位置选择模式

  // 键盘规避相关
  double _keyboardHeight = 0.0;
  bool _isKeyboardVisible = false;
  late AnimationController _keyboardAnimationController;
  late Animation<double> _keyboardAnimation;

  // 知识点数据
  List<MemoryAnchor> _anchors = [];

  // 用于存储每个场景的知识点数据的Map
  final Map<String, List<MemoryAnchor>> _sceneAnchorsCache = {};

  // 相关场景数据 - 根据传入的宫殿数据动态生成
  List<RelatedScene> _relatedScenes = [];
  bool _isBottomBarExpanded = false; // 控制底部预览栏的展开状态

  // 当前场景信息
  String _currentSceneTitle = '图书馆';

  bool _isProcessingTap = false; // 添加防抖标记
  bool _showGuidanceBubble = false; // 控制指导气泡显示 - 只在选择知识点后显示

  final TextEditingController _knowledgeController = TextEditingController();
  final FocusNode _knowledgeFocusNode = FocusNode();
  Vector3? _tapPosition; // 类型从 Offset? 改为 Vector3?

  // 垂直手势状态
  Offset? _verticalPanStartPosition;
  double _verticalPanDelta = 0.0;
  bool _isVerticalPanning = false;
  bool _isPanningVertical = false; // 区分垂直和水平拖拽
  DateTime? _gestureStartTime; // 手势开始时间，用于计算速度

  // 设备感知的手势识别阈值 - 根据设备类型动态调整
  late double _verticalGestureThreshold; // 垂直手势触发阈值
  late double _gestureDirectionThreshold; // 方向判断的最小移动距离
  late double _pointerMoveThreshold; // Pointer事件移动阈值
  late double _pointerDistanceThreshold; // Pointer事件距离阈值
  static const double _verticalDirectionRatio = 2.2; // 垂直分量必须是水平分量的2.2倍
  static const double _horizontalDirectionRatio = 0.7; // 水平分量只需是垂直分量的0.7倍
  late double _velocityThreshold; // 最小速度阈值

  // Cover模式沉浸式体验状态
  bool _isInCoverMode = false; // 当前是否处于Cover模式
  bool _shouldHideUI = false; // 是否应该隐藏UI元素

  // Cover/Contain 模式切换所需的状态
  late AnimationController _viewerAnimationController;
  // Animation<Matrix4>? _viewerAnimation;
  bool _isViewerInitialized = false; // 视图初始化状态
  final Map<int, Size?> _imageSizes = {}; // 每个图片的当前尺寸（可能是压缩后的）
  final Map<int, coord.ImageSizeInfo?> _imageSizeInfos =
      {}; // 完整的图片尺寸信息（包括原始尺寸）
  final Map<int, Matrix4?> _coverMatrices = {}; // 每个图片的Cover模式矩阵
  final Map<int, Matrix4?> _containMatrices = {}; // 每个图片的Contain模式矩阵
  final Map<int, bool> _imageCoverModes =
      {}; // 每个图片的显示模式（false=Contain, true=Cover）

  // 手势冲突解决状态
  final Map<int, bool> _isImageZoomed = {}; // 每个图片的缩放状态
  final Map<int, double> _currentScales = {}; // 每个图片的当前缩放比例
  bool _isPageViewEnabled = true; // PageView是否启用
  static const double _zoomThreshold = 1.05; // 缩放阈值，超过此值认为图片被放大

  // 视觉反馈状态
  late AnimationController _bounceAnimationController;
  late Animation<double> _bounceAnimation;
  bool _isShowingBounceEffect = false;

  // 性能优化状态
  final bool _isPerformanceMode = false; // 性能模式，减少动画和日志

  @override
  void initState() {
    super.initState();

    // 🔄 检查并执行锚点数据迁移
    _checkAndMigrateAnchorData();

    // 初始化图片列表
    _initializeImageList();

    // 初始化页面控制器
    _pageController = PageController(
      initialPage: _currentPageIndex,
      viewportFraction: 1.0, // 100%视口，只显示当前图片
    );

    // 初始化键盘动画控制器
    _keyboardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150), // 快速响应的键盘动画
      vsync: this,
    );

    _keyboardAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _keyboardAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // 初始化视图动画控制器
    _viewerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200), // 快速响应的视图切换动画
      vsync: this,
    );

    // 初始化弹性反馈动画控制器
    _bounceAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _bounceAnimation =
        Tween<double>(
          begin: 0.0,
          end: 8.0, // 8像素的弹性距离
        ).animate(
          CurvedAnimation(
            parent: _bounceAnimationController,
            curve: Curves.fastOutSlowIn, // iOS风格的缓动曲线
          ),
        );

    // 添加键盘监听器
    WidgetsBinding.instance.addObserver(this);

    // 初始化当前场景信息
    _currentSceneTitle = widget.sceneTitle;

    // 根据传入的宫殿数据生成相关场景列表
    _initializeRelatedScenes();

    // 初始化变换控制器
    _initializeTransformControllers();

    // 加载持久化的知识点数据
    _loadPersistedData();
  }

  /// 初始化图片列表
  void _initializeImageList() {
    if (widget.palaceImagePaths != null &&
        widget.palaceImagePaths!.isNotEmpty) {
      _imageList = widget.palaceImagePaths!;
      // 找到当前图片在列表中的索引
      _currentPageIndex = _imageList.indexOf(widget.sceneImagePath);
      if (_currentPageIndex == -1) _currentPageIndex = 0;
    } else {
      _imageList = [widget.sceneImagePath];
      _currentPageIndex = 0;
    }
  }

  /// 初始化变换控制器
  void _initializeTransformControllers() {
    for (int i = 0; i < _imageList.length; i++) {
      _transformControllers[i] = TransformationController();
    }
  }

  /// 初始化设备感知的手势阈值
  void _initializeGestureThresholds() {
    // 获取屏幕信息
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    final devicePixelRatio = mediaQuery.devicePixelRatio;

    // 检测设备类型
    bool isIPad = _isIPadDevice(screenWidth, screenHeight);

    if (isIPad) {
      // iPad 优化的阈值 - 更大的触摸目标和更宽松的检测
      _verticalGestureThreshold = 30.0; // 增加垂直手势阈值
      _gestureDirectionThreshold = 12.0; // 增加方向判断阈值
      _pointerMoveThreshold = 20.0; // 增加Pointer移动阈值
      _pointerDistanceThreshold = 100.0; // 增加Pointer距离阈值
      _velocityThreshold = 20.0; // 降低速度阈值，iPad手势可能较慢

      print('📱 [设备检测] iPad设备 - 使用iPad优化手势阈值');
      print('   - 屏幕尺寸: ${screenWidth.toInt()}x${screenHeight.toInt()}');
      print('   - 像素密度: ${devicePixelRatio.toStringAsFixed(1)}x');
      print('   - 垂直手势阈值: $_verticalGestureThreshold px');
      print('   - Pointer距离阈值: $_pointerDistanceThreshold px');
    } else {
      // iPhone 标准阈值
      _verticalGestureThreshold = 20.0;
      _gestureDirectionThreshold = 8.0;
      _pointerMoveThreshold = 15.0;
      _pointerDistanceThreshold = 80.0;
      _velocityThreshold = 25.0;

      print('📱 [设备检测] iPhone设备 - 使用标准手势阈值');
      print('   - 屏幕尺寸: ${screenWidth.toInt()}x${screenHeight.toInt()}');
      print('   - 像素密度: ${devicePixelRatio.toStringAsFixed(1)}x');
    }
  }

  /// 检测是否为iPad设备
  bool _isIPadDevice(double width, double height) {
    // iPad 检测逻辑：
    // 1. 屏幕宽度通常 > 700px (iPad mini: 768px, iPad: 820px+)
    // 2. 宽高比接近 4:3 或 3:4
    final minDimension = math.min(width, height);
    final maxDimension = math.max(width, height);
    final aspectRatio = maxDimension / minDimension;

    // iPad 特征：最小尺寸 > 700px 且宽高比在 1.2-1.5 之间
    bool sizeCheck = minDimension > 700;
    bool aspectCheck = aspectRatio >= 1.2 && aspectRatio <= 1.5;

    return sizeCheck && aspectCheck;
  }

  /// 初始化设备感知的坐标系统
  void _initializeCoordinateSystem() {
    if (_isCoordinateSystemInitialized) return;

    final mediaQuery = MediaQuery.of(context);
    _devicePixelRatio = mediaQuery.devicePixelRatio;

    // 计算坐标缩放因子 - 修复真机与模拟器的差异
    // 模拟器通常使用1.0或2.0的像素比，真机可能有不同的值
    if (Platform.isIOS) {
      // iOS设备的坐标系统调整
      if (_devicePixelRatio <= 1.0) {
        // 低密度设备或模拟器
        _coordinateScaleFactor = 1.0;
      } else if (_devicePixelRatio <= 2.0) {
        // 标准Retina设备
        _coordinateScaleFactor = 1.0;
      } else {
        // 高密度设备 (iPhone Plus, Pro Max等)
        _coordinateScaleFactor = _devicePixelRatio / 2.0;
      }
    } else if (Platform.isAndroid) {
      // Android设备的坐标系统调整
      if (_devicePixelRatio <= 1.5) {
        // 低密度设备
        _coordinateScaleFactor = 1.0;
      } else if (_devicePixelRatio <= 3.0) {
        // 中高密度设备
        _coordinateScaleFactor = 1.0;
      } else {
        // 超高密度设备
        _coordinateScaleFactor = _devicePixelRatio / 3.0;
      }
    } else {
      // 其他平台使用默认值
      _coordinateScaleFactor = 1.0;
    }

    _isCoordinateSystemInitialized = true;

    print('📱 [坐标系统] 设备信息初始化完成:');
    print(
      '   - 平台: ${Platform.isIOS
          ? "iOS"
          : Platform.isAndroid
          ? "Android"
          : "其他"}',
    );
    print('   - 设备像素比: ${_devicePixelRatio.toStringAsFixed(2)}x');
    print('   - 坐标缩放因子: ${_coordinateScaleFactor.toStringAsFixed(3)}');
    print(
      '   - 屏幕尺寸: ${mediaQuery.size.width.toInt()}x${mediaQuery.size.height.toInt()}',
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 初始化设备感知的坐标系统
    _initializeCoordinateSystem();

    // 初始化设备感知的手势阈值
    _initializeGestureThresholds();

    // 异步初始化所有图片的视图矩阵
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAllImageMatrices();
    });
  }

  /// 初始化所有图片的视图矩阵
  Future<void> _initializeAllImageMatrices() async {
    for (int i = 0; i < _imageList.length; i++) {
      await _initializeImageMatrix(i);
    }

    // 所有图片初始化完成后，设置视图为已初始化状态
    if (mounted) {
      setState(() {
        _isViewerInitialized = true;
      });
    }
  }

  /// 初始化相关场景数据
  void _initializeRelatedScenes() {
    if (widget.palaceImagePaths != null &&
        widget.palaceImagePaths!.isNotEmpty) {
      // 使用传入的宫殿图片数据，按照选择顺序排列
      _relatedScenes = widget.palaceImagePaths!.asMap().entries.map((entry) {
        final index = entry.key;
        final imagePath = entry.value;
        return RelatedScene(
          id: '${widget.sceneId}_$index',
          title: '${widget.sceneTitle} ${index + 1}',
          imagePath: imagePath,
          isSelected: imagePath == widget.sceneImagePath, // 当前图片标记为选中
        );
      }).toList();
    } else {
      // 如果没有传入图片数据，使用当前场景
      _relatedScenes = [
        RelatedScene(
          id: widget.sceneId,
          title: widget.sceneTitle,
          imagePath: widget.sceneImagePath,
          isSelected: true,
        ),
      ];
    }
  }

  @override
  void dispose() {
    // 保存当前场景的数据
    _saveCurrentSceneData();

    // 清理资源
    _pageController.dispose();
    for (final controller in _transformControllers.values) {
      controller.dispose();
    }
    _knowledgeController.dispose();
    _knowledgeFocusNode.dispose();
    _keyboardAnimationController.dispose();
    _viewerAnimationController.dispose();
    _bounceAnimationController.dispose();

    // 移除键盘监听器
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  /// 监听系统指标变化（键盘弹出/收起）
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final newKeyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      final wasKeyboardVisible = _isKeyboardVisible;
      final isKeyboardVisible = newKeyboardHeight > 0;

      if (wasKeyboardVisible != isKeyboardVisible) {
        setState(() {
          _isKeyboardVisible = isKeyboardVisible;
          _keyboardHeight = newKeyboardHeight;
        });

        if (isKeyboardVisible) {
          // 键盘即将显示
          _onKeyboardWillShow(newKeyboardHeight);
        } else {
          // 键盘即将隐藏
          _onKeyboardWillHide();
        }
      } else if (_keyboardHeight != newKeyboardHeight) {
        // 键盘高度发生变化（比如输入法切换）
        setState(() {
          _keyboardHeight = newKeyboardHeight;
        });
      }
    });
  }

  /// 键盘即将显示事件
  void _onKeyboardWillShow(double keyboardHeight) {
    print('🎹 键盘即将显示 - 高度: ${keyboardHeight}px');
    _keyboardAnimationController.forward();
  }

  /// 键盘即将隐藏事件
  void _onKeyboardWillHide() {
    print('🎹 键盘即将隐藏');
    _keyboardAnimationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    // 决定UI是否应该被隐藏
    final bool isUiHidden = _shouldHideUI || _forceHideUI;

    return Scaffold(
      backgroundColor: Colors.black, // 背景设为黑色，适配毛玻璃效果
      resizeToAvoidBottomInset: false, // 避免键盘弹出时UI重绘
      body: Stack(
        children: [
          // 第一层：简洁的纯色背景（移除毛玻璃效果以提升响应速度）
          Container(
            color: Colors.black, // 使用纯黑色背景，简洁高效
          ),

          // 第二层：相册式图片查看器（带弹性反馈效果）
          AnimatedBuilder(
            animation: _bounceAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: _isShowingBounceEffect
                    ? Offset(_bounceAnimation.value, 0)
                    : Offset.zero,
                child: Listener(
                  onPointerDown: _handlePointerDown,
                  onPointerMove: _handlePointerMove,
                  onPointerUp: _handlePointerUp,
                  onPointerCancel: _handlePointerCancel,
                  child: _buildPhotoGalleryViewer(),
                ),
              );
            },
          ),

          // 加载指示器
          if (!_isViewerInitialized)
            const Center(child: CircularProgressIndicator(color: Colors.white)),

          // 第三层：UI浮层
          if (!isUiHidden) _buildTopToolbar(),
          if (_selectedAnchor == null && !_showAddKnowledgePanel && !isUiHidden)
            _buildBottomSceneSelector(),
          if (_selectedAnchor != null) _buildKnowledgeDetailPanel(),
          if (_showAddKnowledgePanel) _buildAddKnowledgePanel(),
          if (_isEditMode && !isUiHidden) _buildEditToolbar(),
        ],
      ),
    );
  }

  /// 手势处理：PointerDown事件
  void _handlePointerDown(PointerDownEvent event) {
    _activePointers.add(event.pointer);
    // 仅在单指触摸时，才开始追踪可能的分享手势
    if (_activePointers.length == 1) {
      _dragStartPointerPosition = event.position;
      _dragDownTimestamp = event.timeStamp; // 记录按下时的时间戳
      _isVerticalDragForShare = false; // 重置状态
    } else {
      // 如果是多指触控（如缩放），则取消分享手势的追踪
      _dragStartPointerPosition = null;
      _dragDownTimestamp = null;
    }
  }

  /// 手势处理：PointerMove事件
  void _handlePointerMove(PointerMoveEvent event) {
    // 如果没有追踪起始点，或为多指操作，则不处理
    if (_dragStartPointerPosition == null || _activePointers.length > 1) {
      return;
    }

    final move = event.position - _dragStartPointerPosition!;

    // 如果分享手势尚未被确认
    if (!_isVerticalDragForShare) {
      // 检查是否为明确的垂直上滑手势
      // 使用设备感知的阈值
      if (move.dy < -_pointerMoveThreshold &&
          move.dy.abs() > move.dx.abs() * 2) {
        _isVerticalDragForShare = true;
        print('🖱️ ✅ 识别为上滑分享手势 (阈值: $_pointerMoveThreshold px)');
      }
      // 检查是否为明确的水平滑动，如果是，则放弃本次分享手势追踪
      else if (move.dx.abs() > _pointerMoveThreshold) {
        _dragStartPointerPosition = null;
        print('🖱️ ❌ 识别为水平滑动，取消分享手势追踪 (阈值: $_pointerMoveThreshold px)');
      }
    }
  }

  /// 手势处理：PointerUp事件
  void _handlePointerUp(PointerUpEvent event) {
    // 仅在单指抬起，且已确认为分享手势时处理
    if (_activePointers.length == 1 &&
        _isVerticalDragForShare &&
        _dragStartPointerPosition != null &&
        _dragDownTimestamp != null) {
      final delta = event.position - _dragStartPointerPosition!;
      final duration = event.timeStamp - _dragDownTimestamp!; // 计算持续时间

      // 使用设备感知的距离阈值
      if (delta.dy < -_pointerDistanceThreshold &&
          duration.inMilliseconds < 500) {
        print(
          '📤 触发分享和UI隐藏: deltaY=${delta.dy.toStringAsFixed(1)}, duration=${duration.inMilliseconds}ms (阈值: $_pointerDistanceThreshold px)',
        );

        // 强制隐藏UI，但不改变图片模式
        setState(() {
          _forceHideUI = true;
        });

        // 触发分享
        _shareCurrentImage();
      } else {
        print(
          '🖱️ 未触发分享: deltaY=${delta.dy.toStringAsFixed(1)}, duration=${duration.inMilliseconds}ms (需要: $_pointerDistanceThreshold px)',
        );
      }
    }

    _activePointers.remove(event.pointer);
    if (_activePointers.isEmpty) {
      _dragStartPointerPosition = null;
      _isVerticalDragForShare = false;
      _dragDownTimestamp = null; // 重置时间戳
    }
  }

  /// 手势处理：PointerCancel事件
  void _handlePointerCancel(PointerCancelEvent event) {
    _activePointers.remove(event.pointer);
    if (_activePointers.isEmpty) {
      _dragStartPointerPosition = null;
      _isVerticalDragForShare = false;
      _dragDownTimestamp = null; // 重置时间戳
    }
  }

  /// 构建相册式图片查看器 - 全屏显示当前图片
  Widget _buildPhotoGalleryViewer() {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: _onPageChanged,
      itemCount: _imageList.length,
      // 优化滚动物理效果，提升快速滑动响应性
      physics: _isPageViewEnabled
          ? const _FastResponseScrollPhysics()
          : const NeverScrollableScrollPhysics(), // 缩放时禁用滚动
      // 启用页面捕捉，确保页面对齐
      pageSnapping: true,
      // 允许页面超出边界，提供iOS风格的弹性效果
      allowImplicitScrolling: false,
      itemBuilder: (context, index) {
        return _buildSingleImageViewer(index);
      },
    );
  }

  /// 构建单个图片查看器
  Widget _buildSingleImageViewer(int index) {
    final imagePath = _imageList[index];
    final transformController = _transformControllers[index];

    if (transformController == null) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    return GestureDetector(
      onTap: () => _handleTap(index),
      onTapDown: (details) => _recordTapPosition(details, index),
      onLongPress: () => _enterEditMode(),
      onDoubleTap: () => _onDoubleTap(index), // 双击切换Cover/Contain模式
      child: GestureDetector(
        // Cover模式下的智能水平手势处理 - 编辑模式或临时气泡显示时禁用
        onPanStart: (_isEditMode || _isPositionSelectionMode)
            ? null
            : (details) => _onCoverModePanStart(details, index),
        onPanUpdate: (_isEditMode || _isPositionSelectionMode)
            ? null
            : (details) => _onCoverModePanUpdate(details, index),
        onPanEnd: (_isEditMode || _isPositionSelectionMode)
            ? null
            : (details) => _onCoverModePanEnd(details, index),
        child: InteractiveViewer(
          transformationController: transformController,
          minScale: 0.1,
          maxScale: 10.0,
          // 编辑模式或临时气泡显示时禁用缩放和平移
          panEnabled: !_isEditMode && !_isPositionSelectionMode,
          scaleEnabled: !_isEditMode && !_isPositionSelectionMode,
          constrained: false,
          // 添加交互监听，实现智能手势冲突解决
          onInteractionStart: (details) => _onInteractionStart(details, index),
          onInteractionUpdate: (details) =>
              _onInteractionUpdate(details, index),
          onInteractionEnd: (details) => _onInteractionEnd(details, index),
          child: Builder(
            builder: (context) {
              final imageSize = _imageSizes[index];
              if (imageSize == null) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              return SizedBox(
                width: imageSize.width,
                height: imageSize.height,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // 清晰的图片内容
                    _buildSceneImage(imagePath),

                    // 锚点覆盖层（仅当前页面显示）
                    if (index == _currentPageIndex &&
                        _showAllAnchors &&
                        !_isPositionSelectionMode)
                      ..._buildAnchorOverlay(index),

                    // 临时位置选择气泡（仅当前页面显示）
                    if (index == _currentPageIndex &&
                        _isPositionSelectionMode &&
                        _tapPosition != null)
                      _buildTempPositionBubble(index),

                    // 指导性知识气泡（仅当前页面显示）
                    if (index == _currentPageIndex &&
                        _showAllAnchors &&
                        _showGuidanceBubble)
                      _buildGuidanceBubble(),

                    // 智能手势检测层 - 编辑模式或临时气泡显示时禁用，避免与拖拽手势冲突
                    if (!_isEditMode && !_isPositionSelectionMode)
                      Positioned.fill(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          // 只在确认为垂直手势时才处理
                          onPanStart: (details) =>
                              _onSmartPanStart(details, index),
                          onPanUpdate: (details) =>
                              _onSmartPanUpdate(details, index),
                          onPanEnd: (details) => _onSmartPanEnd(details, index),
                          child: Container(
                            // 移除这里的颜色，因为它可能会意外地捕获手势
                            // color: Colors.transparent,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 页面切换回调
  void _onPageChanged(int index) {
    setState(() {
      _currentPageIndex = index;
      _currentSceneTitle = '${widget.sceneTitle} ${index + 1}';

      // 更新相关场景选中状态
      for (int i = 0; i < _relatedScenes.length; i++) {
        final scene = _relatedScenes[i];
        _relatedScenes[i] = RelatedScene(
          id: scene.id,
          title: scene.title,
          imagePath: scene.imagePath,
          isSelected: (i == index),
        );
      }

      // 重置当前页面的显示模式为Contain（全局预览）
      _imageCoverModes[index] = false;

      // 重置当前页面的变换矩阵为Contain模式
      final containMatrix = _containMatrices[index];
      final transformController = _transformControllers[index];
      if (containMatrix != null && transformController != null) {
        transformController.value = containMatrix;
      }

      // 加载当前页面的锚点数据
      _loadCurrentPageAnchors();

      // 切换页面时，如果UI是强制隐藏的，则恢复显示
      if (_forceHideUI) {
        setState(() {
          _forceHideUI = false;
        });
      }
    });

    // 检查新页面的Cover模式状态
    _updateCoverModeState();

    // 自动对齐到最近的页面
    _autoAlignToNearestPage();
  }

  /// 自动对齐到最近的页面
  void _autoAlignToNearestPage() {
    if (!_pageController.hasClients) return;

    final currentPage = _pageController.page ?? 0;
    final nearestPage = currentPage.round();

    if ((currentPage - nearestPage).abs() > 0.01) {
      _pageController.animateToPage(
        nearestPage,
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
      );
    }
  }

  /// 处理双击事件 - 切换Cover/Contain模式
  void _onDoubleTap(int index) {
    final transformController = _transformControllers[index];
    if (transformController == null) return;

    // 获取当前图片的显示模式状态
    final currentMode = _imageCoverModes[index] ?? false;
    final targetMatrix = currentMode
        ? _containMatrices[index]
        : _coverMatrices[index];

    if (targetMatrix != null) {
      _animateTransformTo(transformController, targetMatrix);
      setState(() {
        // 只更新当前图片的显示模式
        _imageCoverModes[index] = !currentMode;
      });

      // 更新Cover模式状态，控制UI显示/隐藏
      _updateCoverModeState();
    }
  }

  /// 智能手势开始处理 - 快速方向判断版本
  void _onSmartPanStart(DragStartDetails details, int index) {
    // 只处理当前页面的手势
    if (index != _currentPageIndex) return;

    // 初始化手势状态
    _verticalPanStartPosition = details.globalPosition;
    _verticalPanDelta = 0.0;
    _isVerticalPanning = false;
    _isPanningVertical = false;
    _gestureStartTime = DateTime.now();

    print('🖱️ [智能手势] 手势开始: ${details.globalPosition}');
  }

  /// 智能手势更新处理 - 优化版本，确保全屏上滑分享功能
  void _onSmartPanUpdate(DragUpdateDetails details, int index) {
    // 只处理当前页面的手势
    if (index != _currentPageIndex) return;
    if (_verticalPanStartPosition == null) return;

    final currentPosition = details.globalPosition;
    final deltaX = currentPosition.dx - _verticalPanStartPosition!.dx;
    final deltaY = currentPosition.dy - _verticalPanStartPosition!.dy;
    final absDeltaX = deltaX.abs();
    final absDeltaY = deltaY.abs();

    // 早期方向判断 - 水平手势绝对优先，确保导航流畅性
    if (!_isPanningVertical && !_isVerticalPanning) {
      if (absDeltaY > _gestureDirectionThreshold ||
          absDeltaX > _gestureDirectionThreshold) {
        // 水平手势优先级检查 - 任何明显的水平移动都优先处理
        final isHorizontalGesture =
            absDeltaX > absDeltaY * _horizontalDirectionRatio;
        final isVerticalGesture =
            absDeltaY > absDeltaX * _verticalDirectionRatio;

        // 强制水平优先级：如果水平移动超过12像素，强制认为是水平手势
        final isForceHorizontal = absDeltaX > 12.0;

        // 检查当前图片状态
        final isInCoverMode = _imageCoverModes[index] ?? false;
        final isImageZoomed = _isImageZoomed[index] ?? false;

        if (isHorizontalGesture || isForceHorizontal) {
          // 水平手势处理 - 绝对优先级
          final gestureType = isForceHorizontal ? "强制水平" : "检测水平";
          if (isInCoverMode) {
            // Cover模式下：水平手势用于图片平移
            print(
              '🖱️ 🔍 Cover模式 - $gestureType手势图片平移 (deltaX=${deltaX.toStringAsFixed(1)})',
            );
            _handleCoverModePanInSmartGesture(
              index,
              absDeltaX,
              absDeltaY,
              details,
            );
            return;
          } else if (isImageZoomed) {
            // Contain模式缩放状态：水平手势用于图片平移
            print(
              '🖱️ 🔍 Contain模式缩放 - $gestureType手势图片平移 (deltaX=${deltaX.toStringAsFixed(1)})',
            );
            _resetGestureState();
            return;
          } else if (_isPageViewEnabled) {
            // Contain模式正常状态：水平手势用于PageView切换
            print(
              '🖱️ ➡️ $gestureType手势 - PageView导航优先 (deltaX=${deltaX.toStringAsFixed(1)})',
            );
            _resetGestureState();
            return;
          }
        } else if (isVerticalGesture) {
          // 垂直手势处理 - 全屏上滑分享功能
          _isPanningVertical = true;
          _isVerticalPanning = true;
          print('🖱️ ✅ 垂直手势确认 - 全屏区域有效 (deltaY=${deltaY.toStringAsFixed(1)})');
        }
      }
    }

    // 处理确认的垂直手势 - 支持全屏上滑分享
    if (_isPanningVertical) {
      _onVerticalPanUpdate(details, index);
    }
  }

  /// 智能手势结束处理
  void _onSmartPanEnd(DragEndDetails details, int index) {
    // 只处理确认的垂直手势
    if (_isPanningVertical) {
      _onVerticalPanEnd(details, index);
    }

    // 如果是Cover模式平移，处理惯性滑动
    if (_isCoverPanActive && _coverPanVelocity > 100) {
      _applyCoverModeInertia(index, details.velocity.pixelsPerSecond);
    }

    // 重置Cover模式平移状态
    _isCoverPanActive = false;
    _coverPanVelocity = 0.0;
    _lastPanTime = null;
    _lastPanPosition = null;
    _previousPanPosition = null;

    _resetGestureState();
  }

  /// 重置手势状态
  void _resetGestureState() {
    _verticalPanStartPosition = null;
    _verticalPanDelta = 0.0;
    _isVerticalPanning = false;
    _isPanningVertical = false;
    _gestureStartTime = null;
  }

  // Cover模式下的智能手势处理状态
  Offset? _coverPanStartPosition;
  bool _isCoverPanning = false;
  bool _isHorizontalCoverPan = false;

  // Cover模式下的精确平移控制状态
  bool _isCoverPanActive = false;
  double _coverPanVelocity = 0.0;
  DateTime? _lastPanTime;
  Offset? _lastPanPosition;
  Offset? _previousPanPosition;

  /// Cover模式下的手势开始处理 - 为水平平移做准备，不干扰垂直分享手势
  void _onCoverModePanStart(DragStartDetails details, int index) {
    // 只在Cover模式下处理
    final isInCoverMode = _imageCoverModes[index] ?? false;
    final isImageZoomed = _isImageZoomed[index] ?? false;

    if (!isInCoverMode && !isImageZoomed) return;
    if (index != _currentPageIndex) return;

    // 重置所有Cover模式平移状态 - 专注于水平平移，不干扰垂直分享手势
    _coverPanStartPosition = details.globalPosition;
    _isCoverPanning = false;
    _isHorizontalCoverPan = false;
    _isCoverPanActive = false;
    _coverPanVelocity = 0.0;
    _lastPanTime = DateTime.now();
    _lastPanPosition = details.globalPosition;
    _previousPanPosition = null;

    print('🖱️ [Cover手势] 手势开始 - 准备检测水平平移: ${details.globalPosition}');
  }

  /// Cover模式下的手势更新处理（仅处理水平平移，避免干扰垂直分享手势）
  void _onCoverModePanUpdate(DragUpdateDetails details, int index) {
    // 只在Cover模式下处理
    final isInCoverMode = _imageCoverModes[index] ?? false;
    final isImageZoomed = _isImageZoomed[index] ?? false;

    if (!isInCoverMode && !isImageZoomed) return;
    if (index != _currentPageIndex) return;
    if (_coverPanStartPosition == null) return;

    final currentPosition = details.globalPosition;
    final deltaX = currentPosition.dx - _coverPanStartPosition!.dx;
    final deltaY = currentPosition.dy - _coverPanStartPosition!.dy;

    // 严格的手势方向判断 - 只处理明确的水平手势，避免干扰垂直分享手势
    if (!_isCoverPanning && (deltaX.abs() > 15 || deltaY.abs() > 15)) {
      // 提高水平手势的判断标准，确保不会误判垂直手势
      _isHorizontalCoverPan = deltaX.abs() > deltaY.abs() * 2.0; // 提高比例要求
      final isVerticalGesture = deltaY.abs() > deltaX.abs() * 1.8;

      if (_isHorizontalCoverPan) {
        print('🖱️ [Cover手势] 明确的水平手势 - Cover模式图片水平平移');
        _isCoverPanning = true;
        _isCoverPanActive = true;
      } else if (isVerticalGesture) {
        // 垂直手势不在这里处理，让智能手势检测器处理分享功能
        print('🖱️ [Cover手势] 检测到垂直手势 - 交给智能手势检测器处理分享功能');
        return;
      }
    }

    // 仅处理确认的水平平移
    if (_isCoverPanActive && _isHorizontalCoverPan) {
      _applyCoverModePanWithVerticalBounds(index, deltaX, deltaY, details);
    }
  }

  /// 应用Cover模式下的精确平移（支持垂直边界检测）
  void _applyCoverModePanWithVerticalBounds(
    int index,
    double deltaX,
    double deltaY,
    DragUpdateDetails details,
  ) {
    final transformController = _transformControllers[index];
    if (transformController == null) return;

    // 计算平移速度
    final now = DateTime.now();
    if (_lastPanTime != null && _lastPanPosition != null) {
      final timeDelta = now.difference(_lastPanTime!).inMilliseconds;
      if (timeDelta > 0) {
        final positionDelta = details.globalPosition - _lastPanPosition!;
        _coverPanVelocity = positionDelta.distance / timeDelta * 1000; // 像素/秒
      }
    }
    _lastPanTime = now;
    _lastPanPosition = details.globalPosition;

    // 计算实际的平移增量（相对于上一次位置）
    final currentTranslation = transformController.value.getTranslation();
    final bounds = _calculateCoverPanBounds(index);

    // 计算新的平移位置
    double newX = currentTranslation.x;
    double newY = currentTranslation.y;

    // 水平平移：计算相对于上一帧的增量
    double panDeltaX = 0.0;
    double panDeltaY = 0.0;

    if (_previousPanPosition != null) {
      panDeltaX = details.globalPosition.dx - _previousPanPosition!.dx;
      panDeltaY = details.globalPosition.dy - _previousPanPosition!.dy;
    } else {
      // 第一次平移，使用相对于起始位置的小增量
      panDeltaX = deltaX * 0.1;
      panDeltaY = deltaY * 0.1;
    }

    newX += panDeltaX;
    newY += panDeltaY;
    _previousPanPosition = details.globalPosition;

    // 水平边界检查和限制
    bool hitHorizontalBoundary = false;
    String horizontalBoundaryInfo = '';

    if (newX > bounds['maxX']!) {
      newX = bounds['maxX']!;
      hitHorizontalBoundary = true;
      horizontalBoundaryInfo = '到达左边界 (图片左边缘贴屏幕左边缘)';
    } else if (newX < bounds['minX']!) {
      newX = bounds['minX']!;
      hitHorizontalBoundary = true;
      horizontalBoundaryInfo = '到达右边界 (图片右边缘贴屏幕右边缘)';
    }

    // 垂直边界检查和限制（硬边界停止）
    bool hitVerticalBoundary = false;
    String verticalBoundaryInfo = '';

    if (newY > bounds['maxY']!) {
      newY = bounds['maxY']!;
      hitVerticalBoundary = true;
      verticalBoundaryInfo = '到达顶部边界 (图片顶边贴屏幕顶边)';
    } else if (newY < bounds['minY']!) {
      newY = bounds['minY']!;
      hitVerticalBoundary = true;
      verticalBoundaryInfo = '到达底部边界 (图片底边贴屏幕底边)';
    }

    // 应用新的变换矩阵
    final newMatrix = transformController.value.clone();
    newMatrix.setTranslation(Vector3(newX, newY, currentTranslation.z));
    transformController.value = newMatrix;

    // 边界反馈（仅水平边界触发回弹效果，垂直边界使用硬停止）
    if (hitHorizontalBoundary && !_isShowingBounceEffect) {
      _showBounceEffect();
    }

    // 调试日志
    if (hitHorizontalBoundary || hitVerticalBoundary) {
      final boundaryInfo = [
        horizontalBoundaryInfo,
        verticalBoundaryInfo,
      ].where((s) => s.isNotEmpty).join(', ');
      print(
        '🖱️ 🚫 Cover模式平移: $boundaryInfo - 停止移动 (newX=${newX.toStringAsFixed(1)}, newY=${newY.toStringAsFixed(1)})',
      );
    } else {
      print(
        '🖱️ ✅ Cover模式平移: deltaX=${panDeltaX.toStringAsFixed(1)}, deltaY=${panDeltaY.toStringAsFixed(1)}, newX=${newX.toStringAsFixed(1)}, newY=${newY.toStringAsFixed(1)}',
      );
    }
  }

  /// Cover模式下的手势结束处理
  void _onCoverModePanEnd(DragEndDetails details, int index) {
    // 如果有足够的速度，应用惯性滑动
    if (_isCoverPanActive && _coverPanVelocity > 100) {
      _applyCoverModeInertia(index, details.velocity.pixelsPerSecond);
    }

    // 重置状态
    _coverPanStartPosition = null;
    _isCoverPanning = false;
    _isHorizontalCoverPan = false;
    _isCoverPanActive = false;
    _coverPanVelocity = 0.0;
    _lastPanTime = null;
    _lastPanPosition = null;
    _previousPanPosition = null;
  }

  /// 应用Cover模式下的惯性滑动效果（支持垂直边界检测）
  void _applyCoverModeInertia(int index, Offset velocity) {
    final transformController = _transformControllers[index];
    if (transformController == null) return;

    // 计算惯性滑动的目标位置
    final currentTranslation = transformController.value.getTranslation();
    final bounds = _calculateCoverPanBounds(index);

    // 惯性滑动距离（根据速度计算，添加阻尼）
    final inertiaDistanceX = velocity.dx * 0.3; // 水平阻尼系数
    final inertiaDistanceY = velocity.dy * 0.3; // 垂直阻尼系数

    double targetX = currentTranslation.x + inertiaDistanceX;
    double targetY = currentTranslation.y + inertiaDistanceY;

    // 水平边界限制
    if (targetX > bounds['maxX']!) {
      targetX = bounds['maxX']!;
    } else if (targetX < bounds['minX']!) {
      targetX = bounds['minX']!;
    }

    // 垂直边界限制（硬边界停止）
    if (targetY > bounds['maxY']!) {
      targetY = bounds['maxY']!;
    } else if (targetY < bounds['minY']!) {
      targetY = bounds['minY']!;
    }

    // 创建惯性滑动动画
    final startX = currentTranslation.x;
    final startY = currentTranslation.y;
    final animationController = AnimationController(
      duration: const Duration(milliseconds: 250), // 快速响应的惯性滑动
      vsync: this,
    );

    final animationX = Tween<double>(begin: startX, end: targetX).animate(
      CurvedAnimation(parent: animationController, curve: Curves.decelerate),
    );

    final animationY = Tween<double>(begin: startY, end: targetY).animate(
      CurvedAnimation(parent: animationController, curve: Curves.decelerate),
    );

    animationController.addListener(() {
      final newMatrix = transformController.value.clone();
      newMatrix.setTranslation(
        Vector3(animationX.value, animationY.value, currentTranslation.z),
      );
      transformController.value = newMatrix;
    });

    animationController.forward().then((_) {
      animationController.dispose();
    });

    print(
      '🖱️ [Cover惯性] 速度=(${velocity.dx.toStringAsFixed(1)}, ${velocity.dy.toStringAsFixed(1)}), 目标=(${targetX.toStringAsFixed(1)}, ${targetY.toStringAsFixed(1)})',
    );
  }

  /// 在智能手势处理器中处理Cover模式平移
  void _handleCoverModePanInSmartGesture(
    int index,
    double deltaX,
    double deltaY,
    DragUpdateDetails details,
  ) {
    final transformController = _transformControllers[index];
    if (transformController == null) return;

    // 初始化Cover模式平移状态（如果还没有初始化）
    if (!_isCoverPanActive) {
      _isCoverPanActive = true;
      _lastPanTime = DateTime.now();
      _lastPanPosition = details.globalPosition;
      _previousPanPosition = null;
      print('🖱️ [Cover平移] 初始化Cover模式平移状态');
    }

    // 计算平移速度
    final now = DateTime.now();
    if (_lastPanTime != null && _lastPanPosition != null) {
      final timeDelta = now.difference(_lastPanTime!).inMilliseconds;
      if (timeDelta > 0) {
        final positionDelta = details.globalPosition - _lastPanPosition!;
        _coverPanVelocity = positionDelta.distance / timeDelta * 1000; // 像素/秒
      }
    }

    // 计算实际的平移增量（相对于上一次位置）
    double panDeltaX = 0.0;
    if (_previousPanPosition != null) {
      panDeltaX = details.globalPosition.dx - _previousPanPosition!.dx;
    } else {
      // 第一次平移，使用相对于起始位置的小增量
      panDeltaX = deltaX * 0.1;
    }

    // 应用平移
    final currentTranslation = transformController.value.getTranslation();
    final bounds = _calculateCoverPanBounds(index);

    double newX = currentTranslation.x + panDeltaX;

    // 详细调试信息
    print('🔍 [边界调试] 当前状态:');
    print(
      '   currentX=${currentTranslation.x.toStringAsFixed(1)}, panDeltaX=${panDeltaX.toStringAsFixed(1)}, newX=${newX.toStringAsFixed(1)}',
    );
    print(
      '   边界: minX=${bounds['minX']!.toStringAsFixed(1)}, maxX=${bounds['maxX']!.toStringAsFixed(1)}',
    );
    print(
      '   图片尺寸: ${_imageSizes[index]?.width}x${_imageSizes[index]?.height}, 缩放: ${transformController.value.getMaxScaleOnAxis().toStringAsFixed(2)}',
    );

    // 边界检查和限制 - 修复边界判断逻辑
    bool hitBoundary = false;
    if (newX > bounds['maxX']!) {
      newX = bounds['maxX']!;
      hitBoundary = true;
      print('🖱️ [Cover平移] 到达左边界 (图片左边缘到达屏幕左边缘)');
    } else if (newX < bounds['minX']!) {
      newX = bounds['minX']!;
      hitBoundary = true;
      print('🖱️ [Cover平移] 到达右边界 (图片右边缘到达屏幕右边缘)');
    }

    // 应用新的变换矩阵
    final newMatrix = transformController.value.clone();
    newMatrix.setTranslation(
      Vector3(newX, currentTranslation.y, currentTranslation.z),
    );
    transformController.value = newMatrix;

    // 如果到达边界，触发回弹效果
    if (hitBoundary && !_isShowingBounceEffect) {
      _showBounceEffect();
    }

    // 更新状态
    _lastPanTime = now;
    _lastPanPosition = details.globalPosition;
    _previousPanPosition = details.globalPosition;

    print(
      '🖱️ [Cover平移] deltaX=${panDeltaX.toStringAsFixed(1)}, newX=${newX.toStringAsFixed(1)}, 速度=${_coverPanVelocity.toStringAsFixed(1)}px/s',
    );
  }

  /// 垂直手势更新处理 - 快速滑动优化版本
  void _onVerticalPanUpdate(DragUpdateDetails details, int index) {
    // 只处理当前页面的手势
    if (index != _currentPageIndex) return;
    if (_verticalPanStartPosition == null) {
      if (!_isPerformanceMode) {
        print('🖱️ [DEBUG] 垂直手势更新被忽略: 起始位置为空');
      }
      return;
    }

    // 移除频率限制，确保快速手势能够及时响应
    // 自定义手势识别器已经在底层处理了方向判断，这里只需要处理确认的垂直手势

    final currentPosition = details.globalPosition;
    final deltaY = currentPosition.dy - _verticalPanStartPosition!.dy;

    // 由于使用了自定义手势识别器，到达这里的都是确认的垂直手势
    if (!_isPanningVertical) {
      _isPanningVertical = true;
      _isVerticalPanning = true;
      print('🖱️ ✅ 自定义识别器确认垂直手势');
    }

    // 处理垂直手势
    _verticalPanDelta = deltaY;

    // 检查当前模式并提供相应的反馈
    final isInCoverMode = _imageCoverModes[index] ?? false;

    // 减少日志频率，提升性能
    if (_verticalPanDelta.abs().toInt() % 30 == 0) {
      final gestureType = deltaY < 0 ? "上滑" : "下滑";
      final expectedAction = deltaY < 0
          ? (isInCoverMode ? "图片平移" : "分享功能")
          : "预览功能";
      print(
        '🖱️ 📏 垂直手势更新: deltaY=${deltaY.toStringAsFixed(1)}, $gestureType将触发$expectedAction (${isInCoverMode ? "Cover" : "Contain"}模式)',
      );
    }
  }

  /// 垂直手势结束处理 - 全屏上滑分享优化版本
  void _onVerticalPanEnd(DragEndDetails details, int index) {
    // 只处理当前页面的手势
    if (index != _currentPageIndex) return;
    if (!_isPanningVertical) return;

    // 计算手势速度
    double velocity = 0.0;
    if (_gestureStartTime != null) {
      final duration = DateTime.now()
          .difference(_gestureStartTime!)
          .inMilliseconds;
      if (duration > 0) {
        velocity = _verticalPanDelta.abs() / duration * 1000; // 像素/秒
      }
    }

    // 检查当前图片的显示模式
    final isInCoverMode = _imageCoverModes[index] ?? false;
    print(
      '🖱️ 垂直手势结束: deltaY=${_verticalPanDelta.toStringAsFixed(1)}, 速度=${velocity.toStringAsFixed(1)}px/s, 全屏检测, 模式=${isInCoverMode ? "Cover" : "Contain"}',
    );

    // 判断手势方向和幅度（考虑速度因素，降低阈值以提升响应性）
    final shouldTrigger =
        _verticalPanDelta.abs() > _verticalGestureThreshold ||
        (velocity > _velocityThreshold &&
            _verticalPanDelta.abs() > _verticalGestureThreshold * 0.5);

    if (shouldTrigger) {
      if (_verticalPanDelta < 0) {
        // 上滑手势处理 - 全屏区域有效
        if (isInCoverMode) {
          // Cover模式：上滑用于图片平移
          print('🖱️ Cover模式 - 上滑图片平移');
          _handleImagePanInCoverMode(index, _verticalPanDelta);
        } else {
          // Contain模式：上滑分享当前图片 - 全屏区域都支持
          print('📤 全屏上滑分享触发 - 无延迟响应 (速度=${velocity.toStringAsFixed(1)}px/s)');
          _shareCurrentImage();
        }
      } else {
        // 下滑 - 展开底部预览栏（仅在非Cover模式下可用）
        if (!_isInCoverMode) {
          print('📖 下滑预览触发 (速度=${velocity.toStringAsFixed(1)}px/s)');
          _toggleBottomPreview();
        } else {
          print('📖 Cover模式下禁用下滑预览');
        }
      }
    } else {
      print(
        '🖱️ 手势幅度不足 (距离=${_verticalPanDelta.abs().toStringAsFixed(1)}, 速度=${velocity.toStringAsFixed(1)}px/s)',
      );
    }

    // 重置状态
    _verticalPanStartPosition = null;
    _verticalPanDelta = 0.0;
    _isVerticalPanning = false;
    _isPanningVertical = false;
    _gestureStartTime = null;
  }

  /// InteractiveViewer交互开始监听
  void _onInteractionStart(ScaleStartDetails details, int index) {
    // 记录交互开始时的缩放状态
    final transformController = _transformControllers[index];
    if (transformController != null) {
      final currentScale = transformController.value.getMaxScaleOnAxis();
      _currentScales[index] = currentScale;
      print(
        '🔍 [缩放监听] 交互开始 - 页面$index, 当前缩放: ${currentScale.toStringAsFixed(2)}',
      );
    }
  }

  /// InteractiveViewer交互更新监听
  void _onInteractionUpdate(ScaleUpdateDetails details, int index) {
    final transformController = _transformControllers[index];
    if (transformController != null) {
      final currentScale = transformController.value.getMaxScaleOnAxis();
      _currentScales[index] = currentScale;

      // 检查是否需要更新缩放状态
      final wasZoomed = _isImageZoomed[index] ?? false;
      final isNowZoomed = currentScale > _zoomThreshold;

      if (wasZoomed != isNowZoomed) {
        setState(() {
          _isImageZoomed[index] = isNowZoomed;
          // 只有在非Cover模式下，缩放状态变化才影响PageView
          if (index == _currentPageIndex) {
            final isInCoverMode = _imageCoverModes[index] ?? false;
            if (!isInCoverMode) {
              // 只有Contain模式下才根据缩放状态控制PageView
              _isPageViewEnabled = !isNowZoomed;
            }
            // Cover模式下PageView始终禁用，不受缩放状态影响
          }
        });

        print(
          '🔍 [缩放监听] 缩放状态变化 - 页面$index: ${isNowZoomed ? "已缩放" : "未缩放"} (${currentScale.toStringAsFixed(2)}), PageView: ${_isPageViewEnabled ? "启用" : "禁用"}',
        );
      }
    }
  }

  /// InteractiveViewer交互结束监听
  void _onInteractionEnd(ScaleEndDetails details, int index) {
    final transformController = _transformControllers[index];
    if (transformController != null) {
      final finalScale = transformController.value.getMaxScaleOnAxis();
      _currentScales[index] = finalScale;

      print(
        '🔍 [缩放监听] 交互结束 - 页面$index, 最终缩放: ${finalScale.toStringAsFixed(2)}',
      );

      // 检查是否需要边界反弹效果
      _checkAndApplyBounceEffect(transformController, index);

      // 只有在非Cover模式下，如果缩放比例接近1.0，才自动对齐到精确的1.0
      final isInCoverMode = _imageCoverModes[index] ?? false;
      if (!isInCoverMode && (finalScale - 1.0).abs() < 0.05) {
        final containMatrix = _containMatrices[index];
        if (containMatrix != null) {
          _animateTransformTo(transformController, containMatrix);
          setState(() {
            _isImageZoomed[index] = false;
            _imageCoverModes[index] = false;
            if (index == _currentPageIndex) {
              _isPageViewEnabled = true;
            }
          });
          print('🔍 [缩放监听] 自动对齐到Contain模式');
        }
      }
    }
  }

  /// 检查并应用边界弹性反馈效果
  void _checkAndApplyBounceEffect(
    TransformationController controller,
    int index,
  ) {
    final transform = controller.value;
    final translation = transform.getTranslation();
    final scale = transform.getMaxScaleOnAxis();
    final screenSize = MediaQuery.of(context).size;
    final imageSize = _imageSizes[index];

    if (imageSize == null) return;

    // 计算图片在当前缩放下的实际尺寸
    final scaledWidth = imageSize.width * scale;
    final scaledHeight = imageSize.height * scale;

    // 检查是否超出边界
    bool needsBounce = false;

    // 水平边界检查
    if (scaledWidth > screenSize.width) {
      final maxTranslationX = (scaledWidth - screenSize.width) / 2;
      if (translation.x > maxTranslationX || translation.x < -maxTranslationX) {
        needsBounce = true;
      }
    }

    // 垂直边界检查
    if (scaledHeight > screenSize.height) {
      final maxTranslationY = (scaledHeight - screenSize.height) / 2;
      if (translation.y > maxTranslationY || translation.y < -maxTranslationY) {
        needsBounce = true;
      }
    }

    // 应用弹性反馈效果
    if (needsBounce && !_isShowingBounceEffect) {
      _showBounceEffect();
    }
  }

  /// 显示iOS风格的弹性反馈效果
  void _showBounceEffect() {
    if (_isShowingBounceEffect) return;

    setState(() {
      _isShowingBounceEffect = true;
    });

    _bounceAnimationController.forward().then((_) {
      _bounceAnimationController.reverse().then((_) {
        setState(() {
          _isShowingBounceEffect = false;
        });
      });
    });

    print('🎯 [视觉反馈] 触发边界弹性效果');
  }

  /// 计算Cover模式下图片的平移边界
  Map<String, double> _calculateCoverPanBounds(int index) {
    final transformController = _transformControllers[index];
    final imageSize = _imageSizes[index];
    final screenSize = MediaQuery.of(context).size;

    if (transformController == null || imageSize == null) {
      return {'minX': 0.0, 'maxX': 0.0, 'minY': 0.0, 'maxY': 0.0};
    }

    final transform = transformController.value;
    final scale = transform.getMaxScaleOnAxis();

    // 计算图片在当前缩放下的实际尺寸
    final scaledWidth = imageSize.width * scale;
    final scaledHeight = imageSize.height * scale;

    // Cover模式下的边界计算 - 基于真实内容边界而非居中对齐
    double minTranslationX = 0.0;
    double maxTranslationX = 0.0;
    double minTranslationY = 0.0;
    double maxTranslationY = 0.0;

    // 水平边界计算 - 允许查看图片完整内容
    if (scaledWidth > screenSize.width) {
      // 最右位置：图片左边缘对齐屏幕左边缘
      maxTranslationX = 0.0;

      // 最左位置：图片右边缘对齐屏幕右边缘
      minTranslationX = screenSize.width - scaledWidth;
    }

    // 垂直边界计算 - 允许查看图片完整内容
    if (scaledHeight > screenSize.height) {
      // 最下位置：图片顶边对齐屏幕顶边
      maxTranslationY = 0.0;

      // 最上位置：图片底边对齐屏幕底边
      minTranslationY = screenSize.height - scaledHeight;
    }

    print(
      '🔍 [边界计算v2] 图片缩放尺寸: ${scaledWidth.toStringAsFixed(1)}x${scaledHeight.toStringAsFixed(1)}',
    );
    print(
      '🔍 [边界计算v2] 屏幕尺寸: ${screenSize.width.toStringAsFixed(1)}x${screenSize.height.toStringAsFixed(1)}',
    );
    print(
      '🔍 [边界计算v2] 内容边界: minX=${minTranslationX.toStringAsFixed(1)} (图片右边缘贴右边), maxX=${maxTranslationX.toStringAsFixed(1)} (图片左边缘贴左边)',
    );

    return {
      'minX': minTranslationX,
      'maxX': maxTranslationX,
      'minY': minTranslationY,
      'maxY': maxTranslationY,
    };
  }

  /// 动画变换到目标矩阵
  void _animateTransformTo(
    TransformationController controller,
    Matrix4 targetMatrix,
  ) {
    final animation = Matrix4Tween(begin: controller.value, end: targetMatrix)
        .animate(
          CurvedAnimation(
            parent: _viewerAnimationController,
            curve: Curves.easeInOut,
          ),
        );

    animation.addListener(() {
      controller.value = animation.value;
    });

    _viewerAnimationController.forward(from: 0);
  }

  /// 加载当前页面的锚点数据
  void _loadCurrentPageAnchors() {
    final currentSceneId = _getCurrentSceneId();
    _loadSceneDataFromStorage(currentSceneId);
  }

  /// 构建锚点覆盖层 - 使用标准化坐标系统
  List<Widget> _buildAnchorOverlay(int index) {
    final sizeInfo = _imageSizeInfos[index];
    final transformController = _transformControllers[index];
    if (sizeInfo == null || transformController == null) return [];

    return _anchors.map((anchor) {
      // 🎯 使用标准化坐标系统计算锚点位置
      // 1. 从比例坐标恢复为基于原始图片尺寸的标准化坐标
      final standardizedCoord = coord.StandardizedCoordinate(
        x: anchor.xRatio * sizeInfo.originalSize.width,
        y: anchor.yRatio * sizeInfo.originalSize.height,
        originalImageSize: sizeInfo.originalSize,
      );

      // 2. 将标准化坐标转换为当前图片内坐标
      final currentImageCoord =
          coord.ImageCoordinateSystem.standardizedToCurrentImage(
            standardizedCoord,
            sizeInfo,
          );

      final anchorImageX = currentImageCoord.dx;
      final anchorImageY = currentImageCoord.dy;

      // 🔍 调试标准化坐标转换 - 增强版
      print('🎯 [锚点气泡] 开始构建锚点气泡 ${anchor.id}');
      print('🎯 [锚点气泡] 图片尺寸信息: ${sizeInfo.toString()}');

      // 检查是否为用户导入照片
      final isUserImported =
          widget.sceneImagePath.startsWith('/') ||
          widget.sceneImagePath.contains('file://') ||
          !widget.sceneImagePath.startsWith('http');
      print('🎯 [锚点气泡] 照片类型: ${isUserImported ? "用户导入" : "自带照片"}');

      print(
        '🎯 [锚点气泡] 坐标转换: 比例(${anchor.xRatio.toStringAsFixed(3)}, ${anchor.yRatio.toStringAsFixed(3)}) → 标准化(${standardizedCoord.x.toStringAsFixed(1)}, ${standardizedCoord.y.toStringAsFixed(1)}) → 当前图片内(${anchorImageX.toStringAsFixed(1)}, ${anchorImageY.toStringAsFixed(1)})',
      );

      return Positioned(
        left: anchorImageX,
        top: anchorImageY,
        child: ValueListenableBuilder<Matrix4>(
          valueListenable: transformController,
          builder: (context, transform, child) {
            final scale = transform.getMaxScaleOnAxis();
            final translation = transform.getTranslation();
            final inverseScale = 1.0 / scale;

            // 🔍 调试已保存锚点的平移量信息
            final screenSize = MediaQuery.of(context).size;
            final imageSize = sizeInfo.currentSize;
            final scaleX = screenSize.width / imageSize.width;
            final scaleY = screenSize.height / imageSize.height;
            final containScale = scaleX < scaleY ? scaleX : scaleY;

            // 计算理论平移量（用于验证）
            final theoreticalTranslationX =
                (screenSize.width - imageSize.width * containScale) / 2;
            final theoreticalTranslationY =
                (screenSize.height - imageSize.height * containScale) / 2;

            // 判断照片类型
            String photoType = "截图照片";
            if (imageSize.width < imageSize.height) {
              photoType = "竖屏照片";
            } else if (imageSize.width > imageSize.height) {
              photoType = "横屏照片";
            }

            print('🔍 [锚点气泡-$photoType] 锚点ID: ${anchor.id}');
            print(
              '🔍 [锚点气泡-$photoType] 图片尺寸: ${imageSize.width.toInt()}x${imageSize.height.toInt()}',
            );
            print(
              '🔍 [锚点气泡-$photoType] 理论平移量: (${theoreticalTranslationX.toStringAsFixed(1)}, ${theoreticalTranslationY.toStringAsFixed(1)})',
            );
            print(
              '🔍 [锚点气泡-$photoType] 实际平移量: (${translation.x.toStringAsFixed(1)}, ${translation.y.toStringAsFixed(1)})',
            );

            // 🔧 关键修复：使用与临时气泡完全相同的精确偏移值，确保位置像素级一致
            //
            // 统一偏移策略：
            // 1. 锚点气泡与临时气泡使用完全相同的偏移计算逻辑
            // 2. 所有照片类型（横屏、竖屏、压缩、非压缩）使用相同偏移值
            // 3. 确保拖拽过程中两个圆点精确重叠

            // 检查是否为压缩的用户导入照片
            final isUserImported =
                widget.sceneImagePath.startsWith('/') ||
                widget.sceneImagePath.contains('file://') ||
                !widget.sceneImagePath.startsWith('http');
            final isCompressed = sizeInfo.isCompressed;

            // 🔧 关键修复：与临时气泡使用完全相同的偏移值
            final isDragging = _draggingAnchor?.id == anchor.id;
            double yOffset = isDragging ? -0.97 : -0.94;

            // 🔍 调试日志：记录统一的偏移计算
            print('🎯 [锚点气泡统一偏移] 锚点ID: ${anchor.id}, 拖拽状态: $isDragging');
            print(
              '🎯 [锚点气泡统一偏移] 照片类型: ${isUserImported ? "用户导入" : "自带"}, 压缩状态: $isCompressed',
            );
            print('🎯 [锚点气泡统一偏移] 统一偏移: $yOffset，与临时气泡保持完全一致');

            return FractionalTranslation(
              translation: Offset(-0.5, yOffset),
              child: Transform.scale(scale: inverseScale, child: child),
            );
          },
          child: RepaintBoundary(
            child: KnowledgePointBubble(
              anchor: anchor,
              isSelected: _selectedAnchor?.id == anchor.id,
              isEditMode: _isEditMode,
              isDragging: _draggingAnchor?.id == anchor.id,
              onRepositionStart: (anchor, position) =>
                  _onRepositionStart(anchor, position),
              onRepositionUpdate: (anchor, position) =>
                  _onRepositionUpdate(anchor, position),
              onRepositionEnd: (anchor) => _onRepositionEnd(anchor),
              onTap: () => _selectAnchor(anchor),
            ),
          ),
        ),
      );
    }).toList();
  }

  /// 构建临时位置选择气泡 - 修复定位圆点偏移问题
  Widget _buildTempPositionBubble(int index) {
    final sizeInfo = _imageSizeInfos[index];
    final transformController = _transformControllers[index];
    if (_tapPosition == null ||
        sizeInfo == null ||
        transformController == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      left: _tapPosition!.x, // 直接使用图片内坐标
      top: _tapPosition!.y,
      child: ValueListenableBuilder<Matrix4>(
        valueListenable: transformController,
        builder: (context, transform, child) {
          final scale = transform.getMaxScaleOnAxis();
          final translation = transform.getTranslation();
          final inverseScale = 1.0 / scale;

          // 🔍 调试不同尺寸照片的平移量差异
          final screenSize = MediaQuery.of(context).size;
          final imageSize = sizeInfo.currentSize;
          final scaleX = screenSize.width / imageSize.width;
          final scaleY = screenSize.height / imageSize.height;
          final containScale = scaleX < scaleY ? scaleX : scaleY;

          // 计算理论平移量（用于验证）
          final theoreticalTranslationX =
              (screenSize.width - imageSize.width * containScale) / 2;
          final theoreticalTranslationY =
              (screenSize.height - imageSize.height * containScale) / 2;

          // 判断照片类型
          String photoType = "截图照片";
          if (imageSize.width < imageSize.height) {
            photoType = "竖屏照片";
          } else if (imageSize.width > imageSize.height) {
            photoType = "横屏照片";
          }

          print(
            '🔍 [临时气泡-$photoType] 图片尺寸: ${imageSize.width.toInt()}x${imageSize.height.toInt()}',
          );
          print(
            '🔍 [临时气泡-$photoType] 屏幕尺寸: ${screenSize.width.toInt()}x${screenSize.height.toInt()}',
          );
          print(
            '🔍 [临时气泡-$photoType] 缩放比例: scaleX=${scaleX.toStringAsFixed(3)}, scaleY=${scaleY.toStringAsFixed(3)}, contain=${containScale.toStringAsFixed(3)}',
          );
          print(
            '🔍 [临时气泡-$photoType] 理论平移量: (${theoreticalTranslationX.toStringAsFixed(1)}, ${theoreticalTranslationY.toStringAsFixed(1)})',
          );
          print(
            '🔍 [临时气泡-$photoType] 实际平移量: (${translation.x.toStringAsFixed(1)}, ${translation.y.toStringAsFixed(1)})',
          );

          // 🔧 关键修复：统一使用-1.0偏移值，确保定位圆点精确对准基准位置
          //
          // 临时气泡组件实际尺寸分析：
          // - 文本框：padding(4px上下) + 文本高度(约11px) ≈ 19px
          // - 连接线：正常8px，拖拽时70px
          // - 定位圆点：正常4px，拖拽时6px
          //
          // 🎯 统一偏移策略：
          // - 使用-1.0偏移值，将定位圆点精确对准基准位置（点击位置或锚点位置）
          // - 临时气泡和锚点气泡使用完全相同的偏移计算逻辑
          // - 确保两个圆点在拖拽过程中保持像素级精确重叠

          // 检查是否为压缩的用户导入照片
          final isUserImported =
              widget.sceneImagePath.startsWith('/') ||
              widget.sceneImagePath.contains('file://') ||
              !widget.sceneImagePath.startsWith('http');
          final isCompressed = sizeInfo.isCompressed;

          // 🔧 关键修复：统一所有照片类型的偏移计算逻辑
          //
          // 新的统一策略：
          // 1. 所有照片类型（横屏、竖屏、压缩、非压缩）使用相同的偏移值
          // 2. 基于气泡组件的实际尺寸计算精确偏移
          // 3. 确保定位圆点精确对准点击位置
          //
          // 气泡组件尺寸分析：
          // - 文本框高度：约19px
          // - 连接线高度：正常8px，拖拽时70px
          // - 定位圆点高度：4px
          // - 圆点中心偏移：文本框 + 连接线 + 圆点半径

          double yOffset = -1.0;

          // 🔍 调试日志：记录统一的偏移计算
          print(
            '🎯 [临时气泡统一偏移] 照片类型: ${isUserImported ? "用户导入" : "自带"}, 压缩状态: $isCompressed',
          );
          print('🎯 [临时气泡统一偏移] 拖拽状态: $_isTempBubbleDragging, 统一偏移: $yOffset');
          print('🎯 [临时气泡统一偏移] 所有照片类型使用相同偏移逻辑，确保横屏竖屏一致性');

          return FractionalTranslation(
            translation: Offset(-0.5, yOffset),
            child: Transform.scale(scale: inverseScale, child: child),
          );
        },
        child: RepaintBoundary(
          child: _TempPositionBubble(
            isDragging: _isTempBubbleDragging,
            onDragStart: () => _onTempBubbleDragStart(),
            onDragUpdate: (position) => _onTempBubbleDragUpdate(position),
            onDragEnd: () => _onTempBubbleDragEnd(),
          ),
        ),
      ),
    );
  }

  /// 构建指导性知识气泡 - Notion风格
  Widget _buildGuidanceBubble() {
    return Positioned(
      left: 16,
      right: 16,
      top: MediaQuery.of(context).padding.top + 80, // 在顶部工具栏下方
      child: AnimatedOpacity(
        opacity: _showGuidanceBubble ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.12),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 16,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // 左侧提示图标
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: const Icon(
                  Icons.lightbulb_outline,
                  color: Color(0xFF2E7EED),
                  size: 20,
                ),
              ),

              const SizedBox(width: 12),

              // 提示文本
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      '操作提示',
                      style: TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '您可以移动图层来浏览不同角度，选择记忆锚点查看详情，或点击空白处添加新的知识点',
                      style: TextStyle(
                        color: const Color(0xFF37352F).withValues(alpha: 0.8),
                        fontSize: 13,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              // 关闭按钮
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showGuidanceBubble = false;
                  });
                },
                child: Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: const Color(0xFF37352F).withValues(alpha: 0.06),
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Color(0xFF9B9A97),
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建顶部工具栏 - 悬浮透明样式（灵感来自图一）
  Widget _buildTopToolbar() {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      top: _shouldHideUI ? -100 : 0, // Cover模式下向上隐藏
      left: 0,
      right: 0,
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        opacity: _shouldHideUI ? 0.0 : 1.0,
        child: SafeArea(
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.6),
                  Colors.black.withValues(alpha: 0.3),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
            ),
            child: Row(
              children: [
                // 左侧返回按钮
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                // 中间标题
                Expanded(
                  child: Text(
                    _currentSceneTitle,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // 右侧操作按钮
                IconButton(
                  onPressed: _toggleAnchorVisibility,
                  icon: Icon(
                    _showAllAnchors ? Icons.visibility : Icons.visibility_off,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                IconButton(
                  onPressed: _shareScene,
                  icon: const Icon(Icons.share, color: Colors.white, size: 24),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建底部场景选择器 - V2: 可伸缩、毛玻璃效果
  Widget _buildBottomSceneSelector() {
    // 获取当前选中的场景，用于折叠时显示
    final selectedScene = _relatedScenes.firstWhere(
      (s) => s.isSelected,
      orElse: () => _relatedScenes.isNotEmpty
          ? _relatedScenes.first
          : RelatedScene(
              id: '',
              title: widget.sceneTitle,
              imagePath: '',
              isSelected: false,
            ),
    );

    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      bottom: _shouldHideUI ? -200 : 0, // Cover模式下向下隐藏
      left: 0,
      right: 0,
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        opacity: _shouldHideUI ? 0.0 : 1.0,
        child: SafeArea(
          child: AnimatedCrossFade(
            duration: const Duration(milliseconds: 300),
            crossFadeState: _isBottomBarExpanded
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            firstChild: _buildCollapsedBottomBar(selectedScene.title),
            secondChild: _buildExpandedBottomBar(),
          ),
        ),
      ),
    );
  }

  /// 构建折叠状态的底部栏
  Widget _buildCollapsedBottomBar(String albumTitle) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // 左侧相册标题
          Expanded(
            child: Text(
              '# $albumTitle',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // 右侧展开按钮
          IconButton(
            onPressed: _toggleBottomBar,
            icon: const Icon(
              Icons.keyboard_arrow_up,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建展开状态的底部栏
  Widget _buildExpandedBottomBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.black.withValues(alpha: 0.3),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 顶部操作栏
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '# ${widget.sceneTitle}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    IconButton(
                      onPressed: _toggleBottomBar,
                      icon: const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // 图片预览列表（无间隙）
                SizedBox(
                  height: 60,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _relatedScenes.length,
                    itemBuilder: (context, index) {
                      final scene = _relatedScenes[index];

                      return Padding(
                        padding: EdgeInsets.only(
                          right: index < _relatedScenes.length - 1
                              ? 4
                              : 0, // 最小间隙
                        ),
                        child: GestureDetector(
                          onTap: () => _switchScene(scene),
                          child: Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              border: scene.isSelected
                                  ? Border.all(color: Colors.white, width: 2)
                                  : null,
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              child: _buildSceneImage(scene.imagePath),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 切换底部栏展开/折叠状态
  void _toggleBottomBar() {
    setState(() {
      _isBottomBarExpanded = !_isBottomBarExpanded;
    });
  }

  /// 构建知识点详情面板
  Widget _buildKnowledgeDetailPanel() {
    if (_selectedAnchor == null) return const SizedBox.shrink();

    return Positioned(
      left: 16,
      right: 16,
      bottom: 20, // 调整为底部20px，因为底部预览图已隐藏
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: const Color(0xFF37352F).withValues(alpha: 0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: _getAnchorTypeColor(_selectedAnchor!.type),
                  child: Text(
                    _selectedAnchor!.authorName?.substring(0, 1) ?? '?',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedAnchor!.authorName ?? '匿名用户',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      Text(
                        _formatTime(_selectedAnchor!.createdAt),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF787774),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => setState(() => _selectedAnchor = null),
                  icon: const Icon(
                    Icons.close,
                    color: Color(0xFF9B9A97),
                    size: 20,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Text(
              _selectedAnchor!.content,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF37352F),
                height: 1.5,
              ),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getAnchorTypeColor(
                      _selectedAnchor!.type,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getAnchorTypeLabel(_selectedAnchor!.type),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getAnchorTypeColor(_selectedAnchor!.type),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                // 删除按钮
                GestureDetector(
                  onTap: () => _deleteKnowledgePoint(_selectedAnchor!),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.shade400,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.delete_outline,
                          size: 16,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        const Text(
                          '删除',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // 编辑按钮
                GestureDetector(
                  onTap: () => _editKnowledgePoint(_selectedAnchor!),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2E7EED),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.edit_outlined,
                          size: 16,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        const Text(
                          '编辑',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建知识点添加面板 - 带键盘规避的Notion风格白色输入框
  Widget _buildAddKnowledgePanel() {
    return AnimatedBuilder(
      animation: _keyboardAnimation,
      builder: (context, child) {
        // 实时获取键盘高度
        final currentKeyboardHeight = MediaQuery.of(context).viewInsets.bottom;

        // 计算动态底部位置
        // 当键盘可见时，输入框容器的底边缘应与键盘顶边缘完全贴合（gap = 0）
        final targetBottom = _isKeyboardVisible
            ? currentKeyboardHeight // 紧贴键盘
            : 20.0; // 键盘隐藏时保持20px间距

        final animatedBottom = Tween<double>(
          begin: 20.0,
          end: targetBottom,
        ).animate(_keyboardAnimation).value;

        return AnimatedPositioned(
          duration: const Duration(milliseconds: 250), // 与键盘动画同步
          curve: Curves.easeInOut,
          left: 16,
          right: 16,
          bottom: animatedBottom,
          child: Material(
            color: Colors.transparent,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: const Color(0xFF37352F).withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(
                      alpha: _isKeyboardVisible ? 0.15 : 0.1,
                    ),
                    blurRadius: _isKeyboardVisible ? 16 : 12,
                    offset: Offset(0, _isKeyboardVisible ? 6 : 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // 输入框
                  Expanded(
                    child: TextField(
                      controller: _knowledgeController,
                      focusNode: _knowledgeFocusNode,
                      maxLines: 1,
                      textInputAction: TextInputAction.done,
                      onSubmitted: (_) => _saveKnowledge(),
                      style: const TextStyle(
                        color: Color(0xFF37352F), // 深色文字
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      decoration: const InputDecoration(
                        hintText: '留下要记忆的知识点',
                        hintStyle: TextStyle(
                          color: Color(0xFF9B9A97), // 灰色提示文字
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 4),
                        isDense: true,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // 取消按钮
                  GestureDetector(
                    onTap: () => _cancelAddKnowledge(),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F4), // Notion灰色背景
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Text(
                        '取消',
                        style: TextStyle(
                          color: Color(0xFF787774), // 灰色文字
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 8),

                  // 保存按钮
                  GestureDetector(
                    onTap: () => _saveKnowledge(),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7EED),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFF2E7EED,
                            ).withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Text(
                        '保存',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建编辑模式工具栏 - Notion风格
  Widget _buildEditToolbar() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 60,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: const Color(0xFF37352F).withValues(alpha: 0.15),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            IconButton(
              onPressed: () => _exitEditMode(),
              icon: const Icon(
                Icons.check,
                color: Color(0xFF059669), // 绿色表示完成
                size: 24,
              ),
            ),
            const SizedBox(height: 4),
            IconButton(
              onPressed: () => _addNewAnchor(),
              icon: const Icon(
                Icons.add,
                color: Color(0xFF2E7EED), // 蓝色表示添加
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 选择锚点
  void _selectAnchor(MemoryAnchor anchor) {
    setState(() {
      _selectedAnchor = _selectedAnchor?.id == anchor.id ? null : anchor;

      // 当选择知识点时显示指导气泡
      if (_selectedAnchor != null && !_showGuidanceBubble) {
        _showGuidanceBubble = true;

        // 5秒后自动隐藏指导气泡
        Future.delayed(const Duration(seconds: 5), () {
          if (mounted && _showGuidanceBubble) {
            setState(() {
              _showGuidanceBubble = false;
            });
          }
        });
      }
    });
  }

  /// 进入编辑模式
  void _enterEditMode() {
    setState(() {
      _isEditMode = true;
      _selectedAnchor = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('进入编辑模式，可拖拽调整锚点位置'),
        backgroundColor: const Color(0xFF2E7EED),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 1), // 设置为1秒显示时间
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height - 200,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  /// 退出编辑模式
  void _exitEditMode() {
    setState(() {
      _isEditMode = false;
    });
  }

  // 记录点击位置（在onTapDown中调用）
  Offset? _lastTapPosition;

  /// 记录点击位置 - 统一坐标系统，确保与气泡位置一致
  void _recordTapPosition(TapDownDetails details, int index) {
    // 🔧 关键修复：使用全局坐标系统，确保横屏和竖屏照片的坐标转换一致性
    // 使用globalPosition而不是localPosition，避免不同图片尺寸导致的坐标系差异
    final globalPosition = details.globalPosition;

    // 获取当前图片的变换信息
    final transformController = _transformControllers[index];
    if (transformController == null) return;

    final transform = transformController.value;
    final translation = transform.getTranslation();
    final scale = transform.getMaxScaleOnAxis();

    // 将全局坐标转换为相对于图片容器的坐标
    // 这样可以确保不同尺寸照片使用统一的坐标基准
    final containerRelativeX = globalPosition.dx - translation.x;
    final containerRelativeY = globalPosition.dy - translation.y;

    // 转换为图片内的标准化坐标
    final imageRelativeX = containerRelativeX / scale;
    final imageRelativeY = containerRelativeY / scale;

    _lastTapPosition = Offset(imageRelativeX, imageRelativeY);

    print(
      '👆 [触摸记录] 全局坐标: (${globalPosition.dx.toStringAsFixed(1)}, ${globalPosition.dy.toStringAsFixed(1)})',
    );
    print(
      '👆 [触摸记录] 变换参数: scale=${scale.toStringAsFixed(3)}, translation=(${translation.x.toStringAsFixed(1)}, ${translation.y.toStringAsFixed(1)})',
    );
    print(
      '👆 [触摸记录] 图片内坐标: (${imageRelativeX.toStringAsFixed(1)}, ${imageRelativeY.toStringAsFixed(1)})',
    );
    print('👆 [触摸记录] 使用统一坐标转换逻辑，确保横屏竖屏照片一致性');
  }

  /// 处理点击事件 - V3：使用标准化坐标系统，彻底解决压缩分辨率变化问题
  void _handleTap(int index) {
    if (_isProcessingTap) return;
    if (_lastTapPosition == null) return;
    if (index != _currentPageIndex) return; // 只处理当前页面的点击

    final sizeInfo = _imageSizeInfos[index];
    final transformController = _transformControllers[index];
    if (sizeInfo == null || transformController == null) return;

    _isProcessingTap = true;
    Future.delayed(
      const Duration(milliseconds: 300),
      () => _isProcessingTap = false,
    );

    // 如果正在选择位置或已显示面板，则取消
    if (_isPositionSelectionMode ||
        _selectedAnchor != null ||
        _showAddKnowledgePanel) {
      setState(() {
        _selectedAnchor = null;
        _showAddKnowledgePanel = false;
        _isPositionSelectionMode = false;
        _knowledgeController.clear();
        _tapPosition = null;
      });
      _lastTapPosition = null;
      return;
    }

    // 🔥 关键修复：如果当前在Cover模式下，自动切换回Contain模式
    final isInCoverMode = _imageCoverModes[index] ?? false;
    if (isInCoverMode) {
      print('🎭 [知识点标记] 检测到Cover模式，自动切换回Contain模式以显示UI和恢复PageView');

      // 切换回Contain模式
      final containMatrix = _containMatrices[index];
      if (containMatrix != null) {
        _animateTransformTo(transformController, containMatrix);
        setState(() {
          _imageCoverModes[index] = false;
        });

        // 更新Cover模式状态，恢复UI和PageView
        _updateCoverModeState();

        print('✅ [知识点标记] 已切换到Contain模式，UI和PageView功能已恢复');
      }
    }

    // 🔧 关键修复：使用统一的图片内坐标系统，避免不同照片类型的转换差异
    final imageTapPoint = _lastTapPosition!; // 现在这已经是图片内坐标
    final currentMatrix = transformController.value;

    // 🔍 添加详细调试信息 - 增强版
    print('🔍 [点击处理] ===== 开始处理点击事件 =====');

    // 检查是否为用户导入照片
    final currentImagePath = _imageList[index];
    final isUserImported =
        currentImagePath.startsWith('/') ||
        currentImagePath.contains('file://') ||
        !currentImagePath.startsWith('http');
    print('🔍 [点击处理] 照片类型: ${isUserImported ? "用户导入" : "自带照片"}');
    print('🔍 [点击处理] 图片路径: $currentImagePath');

    print(
      '🔍 [点击处理] 图片内点击坐标: (${imageTapPoint.dx.toStringAsFixed(1)}, ${imageTapPoint.dy.toStringAsFixed(1)})',
    );
    print(
      '🔍 [点击处理] 当前变换矩阵缩放: ${currentMatrix.getMaxScaleOnAxis().toStringAsFixed(3)}',
    );
    print(
      '🔍 [点击处理] 当前变换矩阵平移: (${currentMatrix.getTranslation().x.toStringAsFixed(1)}, ${currentMatrix.getTranslation().y.toStringAsFixed(1)})',
    );
    print(
      '🔍 [点击处理] 当前图片尺寸: ${sizeInfo.currentSize.width}x${sizeInfo.currentSize.height}',
    );
    print(
      '🔍 [点击处理] 原始图片尺寸: ${sizeInfo.originalSize.width}x${sizeInfo.originalSize.height}',
    );

    // 🎯 直接使用图片内坐标进行标准化转换，避免复杂的屏幕坐标转换
    // 这样可以确保横屏、竖屏、压缩照片都使用相同的转换逻辑
    final scaleFactorX =
        sizeInfo.originalSize.width / sizeInfo.currentSize.width;
    final scaleFactorY =
        sizeInfo.originalSize.height / sizeInfo.currentSize.height;

    final standardizedX = imageTapPoint.dx * scaleFactorX;
    final standardizedY = imageTapPoint.dy * scaleFactorY;

    final standardizedCoord = coord.StandardizedCoordinate(
      x: standardizedX,
      y: standardizedY,
      originalImageSize: sizeInfo.originalSize,
    );

    print(
      '🔍 [标准化坐标] 转换结果: 图片内(${imageTapPoint.dx.toStringAsFixed(1)}, ${imageTapPoint.dy.toStringAsFixed(1)}) → 标准化${standardizedCoord.toString()}',
    );

    // 检查点击是否在原始图片边界内
    if (standardizedCoord.x < 0 ||
        standardizedCoord.x > sizeInfo.originalSize.width ||
        standardizedCoord.y < 0 ||
        standardizedCoord.y > sizeInfo.originalSize.height) {
      print('❌ [标准化坐标] 点击超出原始图片边界，忽略');
      print(
        '❌ [标准化坐标] 标准化坐标: (${standardizedCoord.x.toStringAsFixed(1)}, ${standardizedCoord.y.toStringAsFixed(1)}), 边界: (0, 0) - (${sizeInfo.originalSize.width}, ${sizeInfo.originalSize.height})',
      );
      _lastTapPosition = null;
      return; // 点击在图片外，不处理
    }

    // 🔧 关键修复：直接使用图片内坐标作为临时气泡的显示位置
    // 这样避免了复杂的坐标转换，确保横屏竖屏照片的一致性
    final scenePoint = Vector3(imageTapPoint.dx, imageTapPoint.dy, 0);

    print(
      '🎯 精确坐标转换: 图片内坐标直接使用: (${scenePoint.x.toStringAsFixed(1)}, ${scenePoint.y.toStringAsFixed(1)})',
    );

    // 🧪 验证转换正确性：将图片坐标转换回屏幕坐标
    final scale = currentMatrix.getMaxScaleOnAxis();
    final translation = currentMatrix.getTranslation();
    final verifyScreenX = scenePoint.x * scale + translation.x;
    final verifyScreenY = scenePoint.y * scale + translation.y;
    print(
      '🧪 [验证] 反向转换: 图片(${scenePoint.x.toStringAsFixed(1)}, ${scenePoint.y.toStringAsFixed(1)}) → 屏幕(${verifyScreenX.toStringAsFixed(1)}, ${verifyScreenY.toStringAsFixed(1)})',
    );

    // 🎯 关键验证：确保临时气泡将出现在正确位置
    _validateTempBubblePosition(
      scenePoint,
      imageTapPoint,
      scale,
      translation,
      sizeInfo.currentSize,
    );

    // 显示临时气泡和输入面板
    setState(() {
      _isPositionSelectionMode = true;
      _tapPosition = scenePoint; // 保存图片内的精确坐标
      _showAddKnowledgePanel = true;
    });

    // 延迟弹出键盘，给用户时间看到拖拽提示
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted && !_isTempBubbleDragging) {
        FocusScope.of(context).requestFocus(_knowledgeFocusNode);
      }
    });

    _lastTapPosition = null;
  }

  /// 添加新锚点
  void _addNewAnchor() {
    // 允许在任意位置添加，这里可以弹出一个居中输入框或使用预设位置
    _showComingSoon('添加新锚点');
  }

  /// 切换锚点可见性
  void _toggleAnchorVisibility() {
    setState(() {
      _showAllAnchors = !_showAllAnchors;
      if (!_showAllAnchors) {
        _selectedAnchor = null;
      }
    });
  }

  /// 分享场景 - 分享整个相册
  void _shareScene() {
    _showComingSoon('分享整个相册');
  }

  /// 🎯 验证定位圆点对齐精度 - 确保临时气泡和锚点气泡的定位圆点精确重叠
  void _validateTempBubblePosition(
    Vector3 imagePoint,
    Offset imageTapPoint,
    double scale,
    Vector3 translation,
    Size imageSize,
  ) {
    print('🎯 [位置验证] 开始验证临时气泡定位圆点位置一致性');

    // 计算气泡基准位置在图片坐标系中的位置
    final bubbleBaseImageX = imagePoint.x;
    final bubbleBaseImageY = imagePoint.y;

    // 计算气泡基准位置在屏幕上的位置
    final bubbleBaseScreenX = bubbleBaseImageX * scale + translation.x;
    final bubbleBaseScreenY = bubbleBaseImageY * scale + translation.y;

    print(
      '🎯 [位置验证] 图片内点击位置: (${imageTapPoint.dx.toStringAsFixed(1)}, ${imageTapPoint.dy.toStringAsFixed(1)})',
    );
    print(
      '🎯 [位置验证] 气泡基准图片坐标: (${bubbleBaseImageX.toStringAsFixed(1)}, ${bubbleBaseImageY.toStringAsFixed(1)})',
    );

    // 🎯 新增：验证定位圆点对齐精度
    print('🎯 [对齐验证] 临时气泡和锚点气泡的定位圆点应使用相同的偏移计算逻辑');
    print('🎯 [对齐验证] 统一偏移值: 确保定位圆点精确对准基准位置');
    print('🎯 [对齐验证] 两个圆点在拖拽过程中应保持像素级精确重叠');
    print(
      '🎯 [位置验证] 气泡基准屏幕位置: (${bubbleBaseScreenX.toStringAsFixed(1)}, ${bubbleBaseScreenY.toStringAsFixed(1)})',
    );

    // 验证图片内坐标的一致性
    final errorX = (bubbleBaseImageX - imageTapPoint.dx).abs();
    final errorY = (bubbleBaseImageY - imageTapPoint.dy).abs();
    final totalError = math.sqrt(errorX * errorX + errorY * errorY);

    print(
      '🎯 [位置验证] 图片内坐标误差: X=${errorX.toStringAsFixed(1)}px, Y=${errorY.toStringAsFixed(1)}px, 总误差=${totalError.toStringAsFixed(1)}px',
    );
    print('🎯 [位置验证] 注意：定位圆点通过FractionalTranslation精确对准点击位置');

    // 评估位置精度
    if (totalError < 2.0) {
      print('✅ [位置验证] 位置精度优秀 (误差 < 2px)');
    } else if (totalError < 5.0) {
      print('⚠️ [位置验证] 位置精度良好 (误差 < 5px)');
    } else {
      print('❌ [位置验证] 位置精度较差 (误差 >= 5px) - 需要调整');
    }
  }

  /// 分享当前图片 - 增强版本（包含图片、标记点和水印）
  void _shareCurrentImage() async {
    // 防抖检查：防止多个手势系统同时触发分享
    final now = DateTime.now();
    if (_lastShareTime != null &&
        now.difference(_lastShareTime!) < _shareDebounceInterval) {
      print(
        '🚫 分享被防抖机制阻止 (距离上次分享${now.difference(_lastShareTime!).inMilliseconds}ms)',
      );
      return;
    }

    // 更新最后分享时间
    _lastShareTime = now;

    final currentAnchors = _anchors;
    final currentImagePath = _getCurrentImagePath();

    print('📤 开始分享当前图片: $_currentSceneTitle (${currentAnchors.length}个知识点)');

    try {
      // 使用增强分享服务
      await EnhancedShareService.shareImageWithAnnotations(
        imagePath: currentImagePath,
        anchors: currentAnchors,
        sceneTitle: _currentSceneTitle,
        context: context,
      );

      print('✅ 分享操作完成，开始UI恢复');

      // 分享完成后恢复UI状态
      _restoreUIAfterShare();
    } catch (e) {
      print('❌ 分享过程出错: $e');
      // 即使出错也要恢复UI状态
      _restoreUIAfterShare();
    }
  }

  /// 分享完成后恢复UI状态
  void _restoreUIAfterShare() {
    if (!mounted) return;

    print('🔄 开始恢复分享后的UI状态');

    // 1. 恢复UI显示状态
    setState(() {
      _forceHideUI = false;
    });

    // 2. 将当前图片恢复到中央位置
    _resetCurrentImageToCenter();

    print('✅ UI状态恢复完成');
  }

  /// 将当前图片重置到屏幕中央位置
  void _resetCurrentImageToCenter() {
    final currentController = _transformControllers[_currentPageIndex];
    if (currentController == null) return;

    // 获取当前图片的Contain模式矩阵（居中显示）
    final containMatrix = _containMatrices[_currentPageIndex];
    if (containMatrix == null) return;

    print('🎯 重置图片到中央位置 (页面: $_currentPageIndex)');

    // 使用平滑动画将图片恢复到中央位置
    _animateTransformTo(currentController, containMatrix);

    // 更新图片状态为Contain模式
    setState(() {
      _imageCoverModes[_currentPageIndex] = false;
      _isImageZoomed[_currentPageIndex] = false;
      _isPageViewEnabled = true;
    });

    // 更新Cover模式状态，确保UI正确显示
    _updateCoverModeState();

    print('✅ 图片已重置到中央位置，模式已切换为Contain');
  }

  /// 获取当前显示的图片路径
  String _getCurrentImagePath() {
    if (_relatedScenes.isNotEmpty) {
      final currentScene = _relatedScenes.firstWhere(
        (scene) => scene.isSelected,
        orElse: () => _relatedScenes.first,
      );
      return currentScene.imagePath;
    }
    return widget.sceneImagePath;
  }

  /// 切换底部预览栏
  void _toggleBottomPreview() {
    setState(() {
      _isBottomBarExpanded = !_isBottomBarExpanded;
    });

    print('📱 ${_isBottomBarExpanded ? "展开" : "收起"}底部预览栏');
  }

  /// 在Cover模式下处理图片平移（带垂直边界检测）
  void _handleImagePanInCoverMode(int index, double deltaY) {
    final transformController = _transformControllers[index];
    if (transformController == null) {
      print('🖱️ ❌ Cover模式平移失败: transformController为空');
      return;
    }

    // 获取当前变换矩阵和平移状态
    final currentMatrix = transformController.value.clone();
    final currentTranslation = currentMatrix.getTranslation();

    // 计算平移量（上滑为负值，需要向上移动图片，所以平移量为正）
    final panAmount = -deltaY * 0.5; // 调整平移敏感度

    // 计算新的垂直位置
    final newY = currentTranslation.y + panAmount;

    // 获取垂直边界
    final bounds = _calculateCoverPanBounds(index);
    final minY = bounds['minY']!;
    final maxY = bounds['maxY']!;

    // 垂直边界检测 - 硬边界停止
    double finalY = newY;
    bool hitBoundary = false;
    String boundaryInfo = '';

    if (newY > maxY) {
      // 到达顶部边界（图片顶边贴屏幕顶边）
      finalY = maxY;
      hitBoundary = true;
      boundaryInfo = '到达顶部边界';
    } else if (newY < minY) {
      // 到达底部边界（图片底边贴屏幕底边）
      finalY = minY;
      hitBoundary = true;
      boundaryInfo = '到达底部边界';
    }

    // 应用垂直平移（保持水平位置不变）
    currentMatrix.setTranslation(
      Vector3(currentTranslation.x, finalY, currentTranslation.z),
    );

    // 更新变换矩阵
    transformController.value = currentMatrix;

    if (hitBoundary) {
      print(
        '🖱️ 🚫 Cover模式垂直平移: $boundaryInfo - 停止移动 (finalY=${finalY.toStringAsFixed(1)}, 边界范围: ${minY.toStringAsFixed(1)} ~ ${maxY.toStringAsFixed(1)})',
      );
    } else {
      print(
        '🖱️ ✅ Cover模式垂直平移: deltaY=${deltaY.toStringAsFixed(1)}, panAmount=${panAmount.toStringAsFixed(1)}, newY=${finalY.toStringAsFixed(1)}',
      );
    }
  }

  /// 检查并更新Cover模式状态
  void _updateCoverModeState() {
    final currentImageInCoverMode =
        _imageCoverModes[_currentPageIndex] ?? false;

    if (_isInCoverMode != currentImageInCoverMode) {
      setState(() {
        _isInCoverMode = currentImageInCoverMode;
        _shouldHideUI = currentImageInCoverMode;
        // Cover模式下完全禁用PageView切换
        _isPageViewEnabled = !currentImageInCoverMode;
      });

      print(
        '🎭 模式切换: ${_isInCoverMode ? "进入Cover模式 - 隐藏UI，禁用PageView" : "退出Cover模式 - 显示UI，启用PageView"} (PageView启用状态: $_isPageViewEnabled)',
      );

      // 如果进入Cover模式，自动收起底部预览栏
      if (_isInCoverMode && _isBottomBarExpanded) {
        setState(() {
          _isBottomBarExpanded = false;
        });
        print('📱 Cover模式下自动收起底部预览栏');
      }
    }
  }

  /// 切换场景
  void _switchScene(RelatedScene scene) async {
    if (scene.isSelected) return; // 如果已经是当前场景，不执行切换

    // 在PageView模式下，直接切换到对应的页面
    // 方法1：通过scene.id查找索引
    final targetIndex = _relatedScenes.indexWhere((s) => s.id == scene.id);

    // 方法2：通过图片路径查找索引（作为备用方案）
    final targetIndexByPath = _imageList.indexOf(scene.imagePath);

    // 使用找到的有效索引
    final finalTargetIndex = targetIndex != -1
        ? targetIndex
        : targetIndexByPath;

    if (finalTargetIndex != -1 && finalTargetIndex < _imageList.length) {
      // 先保存当前场景的数据
      await _saveCurrentSceneData();

      // 使用PageView的直接跳转到目标页面
      // 经过测试，animateToPage在跨越多个页面时会被中断，jumpToPage更可靠
      _pageController.jumpToPage(finalTargetIndex);

      // 等待一个微任务确保跳转完成
      await Future.microtask(() {});

      // _onPageChanged 会自动处理状态更新，包括：
      // - 更新 _currentPageIndex
      // - 更新 _relatedScenes 的选中状态
      // - 重置显示模式为Contain
      // - 加载锚点数据
    }
  }

  /// 编辑知识点
  void _editKnowledgePoint(MemoryAnchor anchor) {
    _knowledgeController.text = anchor.content;

    // 🔧 关键修复：使用正确的图片内坐标系统
    final sizeInfo = _imageSizeInfos[_currentPageIndex];
    if (sizeInfo == null) return;

    // 从锚点的比例坐标转换为当前图片内坐标
    final standardizedCoord = coord.StandardizedCoordinate(
      x: anchor.xRatio * sizeInfo.originalSize.width,
      y: anchor.yRatio * sizeInfo.originalSize.height,
      originalImageSize: sizeInfo.originalSize,
    );

    final currentImageCoord =
        coord.ImageCoordinateSystem.standardizedToCurrentImage(
          standardizedCoord,
          sizeInfo,
        );

    setState(() {
      _showAddKnowledgePanel = true;
      _selectedAnchor = anchor; // 标记为编辑状态
      _tapPosition = Vector3(currentImageCoord.dx, currentImageCoord.dy, 0);
    });
  }

  /// 删除知识点
  void _deleteKnowledgePoint(MemoryAnchor anchor) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('确认删除'),
        content: Text('确定要删除知识点"${anchor.content}"吗？\n此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _anchors.removeWhere((a) => a.id == anchor.id);
        _selectedAnchor = null;
      });

      // 保存更新后的数据
      await _saveCurrentSceneData();

      // 通知相册管理页面更新统计
      widget.onAnchorCountChanged?.call(widget.sceneId, _anchors.length);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已删除知识点"${anchor.content}"'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(milliseconds: 500), // 设置为0.5秒显示时间
          ),
        );
      }
    }
  }

  /// 获取锚点类型颜色
  Color _getAnchorTypeColor(AnchorType type) {
    switch (type) {
      case AnchorType.motivation:
        return const Color(0xFF2E7EED);
      case AnchorType.goal:
        return const Color(0xFF7C3AED);
      case AnchorType.location:
        return const Color(0xFF0F7B6C);
      case AnchorType.future:
        return const Color(0xFFD9730D);
      case AnchorType.dream:
        return const Color(0xFFE03E3E);
      case AnchorType.study:
        return const Color(0xFF059669);
    }
  }

  /// 获取锚点类型标签
  String _getAnchorTypeLabel(AnchorType type) {
    switch (type) {
      case AnchorType.motivation:
        return '激励';
      case AnchorType.goal:
        return '目标';
      case AnchorType.location:
        return '位置';
      case AnchorType.future:
        return '未来';
      case AnchorType.dream:
        return '梦想';
      case AnchorType.study:
        return '学习';
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 构建场景图片 - 智能处理网络图片和本地文件
  Widget _buildSceneImage(String imagePath) {
    // 判断是否为网络图片
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return Image.network(
        imagePath,
        fit: BoxFit.cover, // 统一使用cover模式
        filterQuality: FilterQuality.high, // 提高图片渲染质量
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: const Color(0xFFFFFFFF),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                        : null,
                    color: const Color(0xFF9B9A97),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '加载中...',
                    style: TextStyle(color: Color(0xFF787774), fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return _buildImageErrorWidget('网络图片加载失败');
        },
      );
    } else {
      // 本地文件图片
      final file = File(imagePath);
      return Image.file(
        file,
        fit: BoxFit.cover,
        filterQuality: FilterQuality.high, // 提高图片渲染质量，特别是对压缩图片
        errorBuilder: (context, error, stackTrace) {
          print('❌ [图片显示] 本地图片加载失败: $imagePath, 错误: $error');
          return _buildImageErrorWidget('本地图片加载失败');
        },
      );
    }
  }

  /// 构建图片加载失败的占位符
  Widget _buildImageErrorWidget(String message) {
    return Container(
      color: const Color(0xFFFFFFFF),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 64,
              color: const Color(0xFF9B9A97).withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(color: Color(0xFF787774), fontSize: 16),
            ),
            const SizedBox(height: 8),
            const Text(
              '点击重试或更换图片',
              style: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 检查并执行锚点数据迁移
  Future<void> _checkAndMigrateAnchorData() async {
    try {
      if (await AnchorDataMigration.needsMigration()) {
        print('🔄 [数据迁移] 检测到需要迁移锚点数据');

        // 获取迁移前的统计信息
        final statsBefore = await AnchorDataMigration.getMigrationStats();
        print('🔄 [数据迁移] 迁移前: $statsBefore');

        // 执行迁移
        await AnchorDataMigration.migrateAnchorData();

        // 获取迁移后的统计信息
        final statsAfter = await AnchorDataMigration.getMigrationStats();
        print('✅ [数据迁移] 迁移后: $statsAfter');

        // 重新加载当前场景数据
        final currentSceneId = _getCurrentSceneId();
        _loadSceneDataFromStorage(currentSceneId);
      } else {
        print('✅ [数据迁移] 锚点数据已是最新版本，无需迁移');
      }
    } catch (e) {
      print('❌ [数据迁移] 迁移检查失败: $e');
    }
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('该功能即将推出，敬请期待'),
        backgroundColor: const Color(0xFF2E7EED),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 生成场景的存储key
  String _getSceneStorageKey(String sceneId) {
    // 使用更详细的key，包含时间戳确保唯一性
    final baseKey =
        'oneday_scene_anchors_palace_${widget.sceneId}_scene_$sceneId';
    return baseKey;
  }

  /// 加载持久化的数据
  Future<void> _loadPersistedData() async {
    try {
      // 调试：列出所有保存的keys
      await _debugListStoredKeys();

      // 确保相关场景列表已经初始化
      if (_relatedScenes.isEmpty) {
        print('⚠️ 相关场景列表为空，跳过数据加载');
        _loadDefaultAnchors();
        return;
      }

      // 获取当前选中场景的正确ID
      final currentSceneId = _getCurrentSceneId();

      // 加载当前场景的数据
      await _loadSceneDataFromStorage(currentSceneId);

      // 通知初始的知识点数量
      _notifyAnchorCountChanged();

      print(
        '📱 数据加载完成 - 当前场景: ${widget.sceneTitle} (ID: $currentSceneId), 知识点数量: ${_anchors.length}',
      );
    } catch (e) {
      print('❌ 加载数据失败: $e');
      // 如果加载失败，使用默认数据
      _loadDefaultAnchors();
    }
  }

  /// 调试：列出所有已保存的keys
  Future<void> _debugListStoredKeys() async {
    // 仅在Debug模式下显示调试信息
    assert(() {
      SharedPreferences.getInstance().then((prefs) {
        final keys = prefs.getKeys();
        final onedayKeys = keys
            .where((key) => key.startsWith('oneday_scene_anchors'))
            .toList();

        print('🗃️ 当前存储的OneDay相关keys (${onedayKeys.length}个):');
        for (final key in onedayKeys) {
          final data = prefs.getString(key);
          print('   📂 $key: ${data?.length ?? 0} 字符');
        }
      });
      return true;
    }());
  }

  /// 从存储加载指定场景的数据
  Future<void> _loadSceneDataFromStorage(String sceneId) async {
    final prefs = await SharedPreferences.getInstance();
    final key = _getSceneStorageKey(sceneId);
    final jsonString = prefs.getString(key);

    if (jsonString != null) {
      try {
        final List<dynamic> jsonList = json.decode(jsonString);
        final anchors = jsonList
            .map((json) => MemoryAnchor.fromJson(json))
            .toList();

        setState(() {
          _anchors = anchors;
          _sceneAnchorsCache[sceneId] = List.from(anchors);
        });

        print('✅ 成功加载场景 $sceneId 的 ${anchors.length} 个知识点');
      } catch (e) {
        print('❌ 解析场景 $sceneId 数据失败: $e');
        _loadDefaultAnchors();
      }
    } else {
      print('🆕 场景 $sceneId 暂无保存的数据，加载默认数据');
      _loadDefaultAnchors();
    }
  }

  /// 初始化空白场景（新相册从空白状态开始）
  void _loadDefaultAnchors() {
    setState(() {
      _anchors = []; // 空白状态，不加载任何模板数据
    });

    print('🆕 初始化空白场景，无预设知识点');
  }

  /// 保存当前场景的数据
  Future<void> _saveCurrentSceneData() async {
    try {
      final currentSceneId = _getCurrentSceneId();

      // 更新缓存
      _sceneAnchorsCache[currentSceneId] = List.from(_anchors);

      // 保存到本地存储
      await _saveSceneDataToStorage(currentSceneId, _anchors);

      print('💾 已保存场景 $currentSceneId 的 ${_anchors.length} 个知识点');
    } catch (e) {
      print('❌ 保存数据失败: $e');
    }
  }

  /// 将指定场景的数据保存到本地存储
  Future<void> _saveSceneDataToStorage(
    String sceneId,
    List<MemoryAnchor> anchors,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final key = _getSceneStorageKey(sceneId);
    final jsonList = anchors.map((anchor) => anchor.toJson()).toList();
    final jsonString = json.encode(jsonList);

    await prefs.setString(key, jsonString);
  }

  /// 获取当前场景ID
  String _getCurrentSceneId() {
    try {
      final currentScene = _relatedScenes.firstWhere(
        (scene) => scene.isSelected,
      );
      return currentScene.id;
    } catch (e) {
      print('❌ 获取当前场景ID失败: $e');
      print(
        '📋 可用场景列表: ${_relatedScenes.map((s) => '${s.id}(${s.isSelected ? "选中" : "未选"})').join(", ")}',
      );
      // 如果找不到选中的场景，返回第一个场景的ID
      if (_relatedScenes.isNotEmpty) {
        return _relatedScenes.first.id;
      }
      return widget.sceneId; // 兜底方案
    }
  }

  /// 取消添加知识点
  void _cancelAddKnowledge() {
    // 隐藏键盘
    FocusScope.of(context).unfocus();

    setState(() {
      _showAddKnowledgePanel = false;
      _knowledgeController.clear();
      _selectedAnchor = null;
      _tapPosition = null;
      // 同时清理位置选择模式
      _isPositionSelectionMode = false;
    });
  }

  /// 保存知识点
  void _saveKnowledge() async {
    if (_knowledgeController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入知识点内容'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 1), // 设置为1秒显示时间
        ),
      );
      return;
    }

    if (_selectedAnchor != null) {
      // 编辑现有知识点
      setState(() {
        _selectedAnchor!.content = _knowledgeController.text.trim();
      });
    } else if (_tapPosition != null) {
      // 添加新知识点 - 使用已计算好的图片内坐标
      final scenePoint = _tapPosition!;

      // 🎯 使用标准化坐标系统保存锚点
      final sizeInfo = _imageSizeInfos[_currentPageIndex];
      if (sizeInfo == null) return;

      // 将当前图片内坐标转换为标准化坐标
      final currentImageCoord = Offset(scenePoint.x, scenePoint.y);
      final standardizedCoord = coord.StandardizedCoordinate(
        x:
            currentImageCoord.dx *
            (sizeInfo.originalSize.width / sizeInfo.currentSize.width),
        y:
            currentImageCoord.dy *
            (sizeInfo.originalSize.height / sizeInfo.currentSize.height),
        originalImageSize: sizeInfo.originalSize,
      );

      // 转换为基于原始图片尺寸的比例坐标
      final xRatio = (standardizedCoord.x / sizeInfo.originalSize.width).clamp(
        0.0,
        1.0,
      );
      final yRatio = (standardizedCoord.y / sizeInfo.originalSize.height).clamp(
        0.0,
        1.0,
      );

      print(
        '💾 [标准化坐标] 保存锚点: 当前图片坐标(${scenePoint.x.toStringAsFixed(1)}, ${scenePoint.y.toStringAsFixed(1)}) → 标准化坐标(${standardizedCoord.x.toStringAsFixed(1)}, ${standardizedCoord.y.toStringAsFixed(1)}) → 比例坐标(${xRatio.toStringAsFixed(3)}, ${yRatio.toStringAsFixed(3)})',
      );
      print(
        '💾 [标准化坐标] 图片尺寸信息: 当前${sizeInfo.currentSize.width}x${sizeInfo.currentSize.height}, 原始${sizeInfo.originalSize.width}x${sizeInfo.originalSize.height}',
      );

      final newAnchor = MemoryAnchor(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        xRatio: xRatio.clamp(0.0, 1.0),
        yRatio: yRatio.clamp(0.0, 1.0),
        content: _knowledgeController.text.trim(),
        authorName: '我',
        likes: 0,
        createdAt: DateTime.now(),
        type: AnchorType.study,
      );

      setState(() {
        _anchors.add(newAnchor);
      });

      print(
        '📍 新建锚点: ID=${newAnchor.id}, 位置=(${xRatio.toStringAsFixed(3)}, ${yRatio.toStringAsFixed(3)})',
      );
    }

    // 立即保存到本地存储
    await _saveCurrentSceneData();

    // 触发添加记忆锚点成就
    ref.read(achievementProvider.notifier).onMemoryAnchorAdded(widget.sceneId);

    // 通知知识点数量变化
    _notifyAnchorCountChanged();

    _cancelAddKnowledge();
  }

  /// 通知知识点数量变化
  void _notifyAnchorCountChanged() {
    if (widget.onAnchorCountChanged != null) {
      // 计算当前场景的总知识点数量
      int totalAnchors = 0;
      for (final anchors in _sceneAnchorsCache.values) {
        totalAnchors += anchors.length;
      }
      // 加上当前场景的知识点（如果还没有缓存）
      if (!_sceneAnchorsCache.containsKey(_getCurrentSceneId())) {
        totalAnchors += _anchors.length;
      }

      widget.onAnchorCountChanged!(widget.sceneId, totalAnchors);
      print('📊 通知知识点数量变化: 场景ID=${widget.sceneId}, 总数=$totalAnchors');
    }
  }

  // 拖拽状态管理
  MemoryAnchor? _draggingAnchor;

  // 临时气泡拖拽状态管理
  bool _isTempBubbleDragging = false;

  // 拖拽偏移量记录，用于防止跳变
  Offset? _dragStartOffset;

  /// 新的直接拖拽系统 - 开始拖拽
  void _onRepositionStart(MemoryAnchor anchor, Offset startPosition) {
    print('🎯 [拖拽] 开始拖拽锚点: ${anchor.id}');
    print(
      '🎯 [拖拽] 拖拽开始位置: ${startPosition.dx.toStringAsFixed(1)}, ${startPosition.dy.toStringAsFixed(1)}',
    );

    // 触觉反馈
    HapticFeedback.lightImpact();

    // 🔧 关键修复：立即计算拖拽开始时的偏移量，防止跳变
    final currentAnchorPosition = _getAnchorGlobalPosition(anchor);
    if (currentAnchorPosition != null) {
      _dragStartOffset = Offset(
        startPosition.dx - currentAnchorPosition.dx,
        startPosition.dy - currentAnchorPosition.dy,
      );
      print(
        '🎯 [拖拽] 立即计算偏移量: ${_dragStartOffset!.dx.toStringAsFixed(1)}, ${_dragStartOffset!.dy.toStringAsFixed(1)}',
      );
      print(
        '🎯 [拖拽] 锚点当前位置: ${currentAnchorPosition.dx.toStringAsFixed(1)}, ${currentAnchorPosition.dy.toStringAsFixed(1)}',
      );
    } else {
      _dragStartOffset = Offset.zero;
      print('⚠️ [拖拽] 无法获取锚点位置，使用零偏移');
    }

    setState(() {
      _draggingAnchor = anchor;
    });

    // 🎯 验证锚点拖拽开始时的圆点对齐
    _validateDotAlignment('锚点拖拽开始');

    // 🔧 验证拖拽启动的平滑性
    _validateDragSmoothStart(anchor, startPosition);
  }

  /// 🔧 验证拖拽启动的平滑性 - 确保无跳变
  void _validateDragSmoothStart(MemoryAnchor anchor, Offset startPosition) {
    print('🔧 [平滑拖拽] ===== 拖拽启动验证 =====');

    if (_dragStartOffset != null) {
      final expectedAnchorPosition = Offset(
        startPosition.dx - _dragStartOffset!.dx,
        startPosition.dy - _dragStartOffset!.dy,
      );

      final currentAnchorPosition = _getAnchorGlobalPosition(anchor);
      if (currentAnchorPosition != null) {
        final positionDiff = Offset(
          (expectedAnchorPosition.dx - currentAnchorPosition.dx).abs(),
          (expectedAnchorPosition.dy - currentAnchorPosition.dy).abs(),
        );

        print(
          '🔧 [平滑拖拽] 手指位置: ${startPosition.dx.toStringAsFixed(1)}, ${startPosition.dy.toStringAsFixed(1)}',
        );
        print(
          '🔧 [平滑拖拽] 锚点当前位置: ${currentAnchorPosition.dx.toStringAsFixed(1)}, ${currentAnchorPosition.dy.toStringAsFixed(1)}',
        );
        print(
          '🔧 [平滑拖拽] 计算偏移量: ${_dragStartOffset!.dx.toStringAsFixed(1)}, ${_dragStartOffset!.dy.toStringAsFixed(1)}',
        );
        print(
          '🔧 [平滑拖拽] 预期锚点位置: ${expectedAnchorPosition.dx.toStringAsFixed(1)}, ${expectedAnchorPosition.dy.toStringAsFixed(1)}',
        );
        print(
          '🔧 [平滑拖拽] 位置差异: ${positionDiff.dx.toStringAsFixed(1)}, ${positionDiff.dy.toStringAsFixed(1)}',
        );

        if (positionDiff.dx < 2.0 && positionDiff.dy < 2.0) {
          print('🔧 [平滑拖拽] ✅ 拖拽启动平滑，无跳变（差异<2px）');
        } else {
          print('🔧 [平滑拖拽] ❌ 检测到潜在跳变（差异≥2px）');
        }
      }
    } else {
      print('🔧 [平滑拖拽] ❌ 偏移量未计算，可能发生跳变');
    }

    print('🔧 [平滑拖拽] ==================');
  }

  /// 新的直接拖拽系统 - 更新拖拽位置
  void _onRepositionUpdate(MemoryAnchor anchor, Offset globalPosition) {
    if (_draggingAnchor?.id != anchor.id) return;

    print(
      '🎯 [拖拽] 更新拖拽位置: ${globalPosition.dx.toStringAsFixed(1)}, ${globalPosition.dy.toStringAsFixed(1)}',
    );

    // 🔧 关键修复：直接应用预先计算好的偏移量，确保平滑拖拽
    if (_dragStartOffset == null) {
      print('⚠️ [拖拽] 偏移量未初始化，跳过此次更新');
      return;
    }

    // 应用偏移量修正，确保锚点位置平滑跟随手指
    final correctedPosition = Offset(
      globalPosition.dx - _dragStartOffset!.dx,
      globalPosition.dy - _dragStartOffset!.dy,
    );

    print(
      '🎯 [拖拽] 偏移修正: 原始(${globalPosition.dx.toStringAsFixed(1)}, ${globalPosition.dy.toStringAsFixed(1)}) → 修正后(${correctedPosition.dx.toStringAsFixed(1)}, ${correctedPosition.dy.toStringAsFixed(1)})',
    );

    // 检查位置是否在图片边界内（可选的预检查）
    if (!_isPositionInImageBounds(correctedPosition)) {
      print('⚠️ [拖拽] 位置超出图片边界，但仍允许拖拽（边界限制在坐标转换中处理）');
    }

    // 将修正后的全局坐标转换为相对于图片的坐标（包含边界检测）
    final newPosition = _convertGlobalToImageCoordinate(correctedPosition);
    if (newPosition == null) return;

    setState(() {
      anchor.xRatio = newPosition.dx;
      anchor.yRatio = newPosition.dy;
    });
  }

  /// 新的直接拖拽系统 - 结束拖拽
  void _onRepositionEnd(MemoryAnchor anchor) {
    print('🎯 [拖拽] 结束拖拽锚点: ${anchor.id}');

    // 🎯 验证锚点拖拽结束时的圆点对齐
    _validateDotAlignment('锚点拖拽结束');

    // 触觉反馈
    HapticFeedback.mediumImpact();

    setState(() {
      _draggingAnchor = null;
      _dragStartOffset = null; // 清除偏移量记录
    });

    // 保存位置
    _saveCurrentSceneData();
  }

  /// 获取锚点的当前全局位置（用于计算拖拽偏移量）
  /// 🔧 关键修复：必须与已保存锚点的实际显示位置完全一致，包括FractionalTranslation偏移
  Offset? _getAnchorGlobalPosition(MemoryAnchor anchor) {
    try {
      final sizeInfo = _imageSizeInfos[_currentPageIndex];
      final transformController = _transformControllers[_currentPageIndex];
      if (sizeInfo == null || transformController == null) return null;

      // 🎯 使用标准化坐标系统计算锚点的当前屏幕位置
      // 1. 从比例坐标恢复为基于原始图片尺寸的标准化坐标
      final standardizedCoord = coord.StandardizedCoordinate(
        x: anchor.xRatio * sizeInfo.originalSize.width,
        y: anchor.yRatio * sizeInfo.originalSize.height,
        originalImageSize: sizeInfo.originalSize,
      );

      // 2. 将标准化坐标转换为当前图片内坐标
      final currentImageCoord =
          coord.ImageCoordinateSystem.standardizedToCurrentImage(
            standardizedCoord,
            sizeInfo,
          );

      // 3. 手动实现图片内坐标到屏幕全局坐标的转换
      final transform = transformController.value;

      // 应用变换矩阵将图片内坐标转换为屏幕坐标
      final transformedPoint = transform.transform3(
        Vector3(currentImageCoord.dx, currentImageCoord.dy, 0.0),
      );

      // 4. 🔧 关键修复：应用与已保存锚点显示时相同的FractionalTranslation偏移
      final scale = transform.getMaxScaleOnAxis();
      final inverseScale = 1.0 / scale;

      // 计算与_buildAnchorOverlay中完全相同的偏移值
      final isUserImported =
          widget.sceneImagePath.startsWith('/') ||
          widget.sceneImagePath.contains('file://') ||
          !widget.sceneImagePath.startsWith('http');
      final isCompressed = sizeInfo.isCompressed;

      // 🔧 关键修复：统一锚点全局位置计算的偏移逻辑
      //
      // 问题分析：
      // 这个函数用于计算锚点的全局位置，用于拖拽偏移量计算
      // 如果这里的偏移计算与显示时不一致，会导致拖拽开始时的位置跳变
      //
      // 解决方案：
      // 使用与锚点气泡显示时完全相同的偏移计算逻辑

      // 判断是否为拖拽状态（这里无法直接判断，使用正常状态偏移）
      double yOffset = -1.0; // 使用正常状态的偏移值，与锚点气泡显示保持一致

      // 🔍 调试日志：记录统一的偏移计算
      print(
        '🎯 [锚点全局位置统一偏移] 照片类型: ${isUserImported ? "用户导入" : "自带"}, 压缩状态: $isCompressed, 统一偏移: $yOffset',
      );

      // 移除用户导入照片的特殊调整逻辑，确保与锚点气泡显示行为一致
      // if (isUserImported && isCompressed) {
      //   // 已移除特殊调整逻辑
      // }

      // 应用FractionalTranslation偏移，确保与实际显示位置一致
      final fractionalOffset = Offset(-0.5, yOffset);
      final actualGlobalPosition = Offset(
        transformedPoint.x + (fractionalOffset.dx * inverseScale),
        transformedPoint.y + (fractionalOffset.dy * inverseScale),
      );

      print(
        '🎯 [拖拽] 锚点位置计算: 比例(${anchor.xRatio.toStringAsFixed(3)}, ${anchor.yRatio.toStringAsFixed(3)}) → 图片内(${currentImageCoord.dx.toStringAsFixed(1)}, ${currentImageCoord.dy.toStringAsFixed(1)}) → 变换后(${transformedPoint.x.toStringAsFixed(1)}, ${transformedPoint.y.toStringAsFixed(1)}) → 最终全局(${actualGlobalPosition.dx.toStringAsFixed(1)}, ${actualGlobalPosition.dy.toStringAsFixed(1)})',
      );

      return actualGlobalPosition;
    } catch (e) {
      print('❌ [拖拽] 获取锚点全局位置失败: $e');
      return null;
    }
  }

  /// 将全局坐标转换为标准化坐标系统的相对坐标 (0.0-1.0)，包含边界检测
  Offset? _convertGlobalToImageCoordinate(Offset globalPosition) {
    try {
      final sizeInfo = _imageSizeInfos[_currentPageIndex];
      if (sizeInfo == null) return null;

      final currentController = _transformControllers[_currentPageIndex];
      if (currentController == null) return null;

      final transform = currentController.value;

      // 🎯 使用标准化坐标系统进行转换：全局坐标 → 标准化坐标
      final standardizedCoord =
          coord.ImageCoordinateSystem.screenToStandardized(
            globalPosition,
            transform,
            sizeInfo,
          );

      // 转换为相对坐标 (0.0-1.0) - 基于原始图片尺寸
      final relativeX = standardizedCoord.x / sizeInfo.originalSize.width;
      final relativeY = standardizedCoord.y / sizeInfo.originalSize.height;

      // 边界检测和限制
      final clampedX = _clampWithBoundaryFeedback(relativeX, 0.0, 1.0, 'X');
      final clampedY = _clampWithBoundaryFeedback(relativeY, 0.0, 1.0, 'Y');

      print(
        '🔄 [标准化坐标转换] 全局(${globalPosition.dx.toStringAsFixed(1)}, ${globalPosition.dy.toStringAsFixed(1)}) → 标准化(${standardizedCoord.x.toStringAsFixed(1)}, ${standardizedCoord.y.toStringAsFixed(1)}) → 相对(${clampedX.toStringAsFixed(3)}, ${clampedY.toStringAsFixed(3)})',
      );

      return Offset(clampedX, clampedY);
    } catch (e) {
      print('❌ [坐标转换] 转换失败: $e');
      return null;
    }
  }

  /// 带边界反馈的坐标限制
  double _clampWithBoundaryFeedback(
    double value,
    double min,
    double max,
    String axis,
  ) {
    if (value < min) {
      print('🚫 [边界检测] $axis轴超出下边界: ${value.toStringAsFixed(3)} < $min');
      // 轻微震动反馈
      HapticFeedback.selectionClick();
      return min;
    } else if (value > max) {
      print('🚫 [边界检测] $axis轴超出上边界: ${value.toStringAsFixed(3)} > $max');
      // 轻微震动反馈
      HapticFeedback.selectionClick();
      return max;
    }
    return value;
  }

  /// 检查坐标是否在图片可视区域内（使用标准化坐标系统）
  bool _isPositionInImageBounds(Offset globalPosition) {
    try {
      final sizeInfo = _imageSizeInfos[_currentPageIndex];
      if (sizeInfo == null) return false;

      final currentController = _transformControllers[_currentPageIndex];
      if (currentController == null) return false;

      final transform = currentController.value;

      // 🎯 使用标准化坐标系统进行边界检测
      final standardizedCoord =
          coord.ImageCoordinateSystem.screenToStandardized(
            globalPosition,
            transform,
            sizeInfo,
          );

      // 检查标准化坐标是否在原始图片边界内
      final inBounds =
          standardizedCoord.x >= 0 &&
          standardizedCoord.x <= sizeInfo.originalSize.width &&
          standardizedCoord.y >= 0 &&
          standardizedCoord.y <= sizeInfo.originalSize.height;

      if (!inBounds) {
        print(
          '🚫 [边界检测] 位置超出图片边界: 全局(${globalPosition.dx.toStringAsFixed(1)}, ${globalPosition.dy.toStringAsFixed(1)}) → 标准化(${standardizedCoord.x.toStringAsFixed(1)}, ${standardizedCoord.y.toStringAsFixed(1)})',
        );
      }

      return inBounds;
    } catch (e) {
      print('❌ [边界检测] 检查失败: $e');
      return false;
    }
  }

  /// 临时气泡拖拽系统 - 开始拖拽
  void _onTempBubbleDragStart() {
    // 触觉反馈
    HapticFeedback.lightImpact();

    setState(() {
      _isTempBubbleDragging = true;
    });

    // 🎯 验证拖拽开始时的圆点对齐
    _validateDotAlignment('拖拽开始');

    // 🔧 横屏状态验证
    _validateLandscapeOrientation('拖拽开始');
  }

  /// 🔧 横屏状态验证方法
  void _validateLandscapeOrientation(String phase) {
    final orientation = MediaQuery.of(context).orientation;
    final isLandscape = orientation == Orientation.landscape;

    if (isLandscape) {
      print('🔄 [横屏验证] ===== $phase - 横屏状态 =====');

      final screenSize = MediaQuery.of(context).size;
      print(
        '🔄 [横屏验证] 屏幕尺寸: ${screenSize.width.toStringAsFixed(1)} x ${screenSize.height.toStringAsFixed(1)}',
      );

      // 检查当前临时气泡的位置
      if (_tapPosition != null) {
        print(
          '🔄 [横屏验证] 当前临时气泡位置: ${_tapPosition!.x.toStringAsFixed(1)}, ${_tapPosition!.y.toStringAsFixed(1)}',
        );
      }

      // 检查变换矩阵
      final currentController = _transformControllers[_currentPageIndex];
      if (currentController != null) {
        final transform = currentController.value;
        final scale = transform.getMaxScaleOnAxis();
        final translation = transform.getTranslation();
        print(
          '🔄 [横屏验证] 变换矩阵 - 缩放: ${scale.toStringAsFixed(3)}, 平移: ${translation.x.toStringAsFixed(1)}, ${translation.y.toStringAsFixed(1)}',
        );
      }

      // 检查图片尺寸信息
      final sizeInfo = _imageSizeInfos[_currentPageIndex];
      if (sizeInfo != null) {
        print(
          '🔄 [横屏验证] 图片尺寸: ${sizeInfo.currentSize.width.toStringAsFixed(1)} x ${sizeInfo.currentSize.height.toStringAsFixed(1)}',
        );
      }

      print('🔄 [横屏验证] ==================');
    }
  }

  /// 🎯 验证定位圆点对齐精度的专用方法 - 确保三个计算逻辑完全一致
  void _validateDotAlignment(String phase) {
    print('🎯 [圆点对齐验证] ===== $phase =====');
    print('🎯 [圆点对齐验证] 临时气泡拖拽状态: $_isTempBubbleDragging');
    print('🎯 [圆点对齐验证] 当前拖拽锚点: ${_draggingAnchor?.id ?? "无"}');

    // 🔍 详细验证三个计算逻辑的一致性
    _validateOffsetCalculationConsistency();

    print('🎯 [圆点对齐验证] 统一偏移值-1.0确保定位圆点精确对准基准位置');
    print('🎯 [圆点对齐验证] ==================');
  }

  /// 🔍 验证偏移计算一致性的详细方法
  void _validateOffsetCalculationConsistency() {
    final sizeInfo = _imageSizeInfos[_currentPageIndex];
    if (sizeInfo == null) return;

    // 检查照片类型（用于调试信息）
    final isUserImported =
        widget.sceneImagePath.startsWith('/') ||
        widget.sceneImagePath.contains('file://') ||
        !widget.sceneImagePath.startsWith('http');
    final isCompressed = sizeInfo.isCompressed;

    // 🔧 修复后的统一偏移计算验证
    // 所有照片类型现在都使用相同的基础偏移值，不再有特殊调整

    // 计算三个逻辑的偏移值（修复后）
    final tempBubbleOffset = -0.9355; // 临时气泡显示逻辑（正常状态）
    final anchorBubbleOffset = -0.9355; // 锚点气泡显示逻辑（正常状态）
    final dragUpdateOffset = 0.0; // 拖拽更新逻辑（无特殊偏移）

    print(
      '🔍 [统一偏移验证] 照片类型: ${isUserImported ? "用户导入" : "自带"}, 压缩状态: $isCompressed',
    );
    print('🔍 [一致性验证] 临时气泡偏移: $tempBubbleOffset');
    print('🔍 [一致性验证] 锚点气泡偏移: $anchorBubbleOffset');
    print('🔍 [一致性验证] 拖拽更新偏移: $dragUpdateOffset');

    // 验证一致性
    final isConsistent =
        (tempBubbleOffset - anchorBubbleOffset).abs() < 0.0001 &&
        (tempBubbleOffset - dragUpdateOffset).abs() < 0.0001;

    if (isConsistent) {
      print('🔍 [一致性验证] ✅ 三个计算逻辑完全一致，圆点应精确重叠');
    } else {
      print('🔍 [一致性验证] ❌ 计算逻辑不一致，存在对齐问题');
    }
  }

  /// 临时气泡拖拽系统 - 更新拖拽位置（从文本框位置计算定位圆点位置）
  void _onTempBubbleDragUpdate(Offset textBoxGlobalPosition) {
    if (!_isTempBubbleDragging) return;

    print('🔄 [拖拽修复] ===== 拖拽位置更新开始 =====');
    print(
      '🔄 [拖拽修复] 接收到文本框全局位置: ${textBoxGlobalPosition.dx.toStringAsFixed(1)}, ${textBoxGlobalPosition.dy.toStringAsFixed(1)}',
    );

    // 获取当前变换信息
    final currentController = _transformControllers[_currentPageIndex];
    if (currentController == null) return;

    final transform = currentController.value;
    final scale = transform.getMaxScaleOnAxis();
    final translation = transform.getTranslation();

    print(
      '🔄 [拖拽修复] 当前变换: 缩放=${scale.toStringAsFixed(3)}, 平移=(${translation.x.toStringAsFixed(1)}, ${translation.y.toStringAsFixed(1)})',
    );

    // 🔧 关键修复：计算定位圆点的全局位置
    //
    // 问题分析：
    // 1. _tapPosition 存储的是定位圆点在图片内的坐标
    // 2. textBoxGlobalPosition 是文本框的全局坐标
    // 3. 需要将文本框位置转换为定位圆点位置，然后更新 _tapPosition
    //
    // 解决方案：
    // 1. 根据气泡结构计算文本框到定位圆点的偏移量
    // 2. 将文本框全局位置转换为定位圆点全局位置
    // 3. 将定位圆点全局位置转换为图片内坐标

    // 🎯 气泡结构分析（基于实际测量）
    // - 文本框高度：约25px（包含padding和边框）
    // - 连接线高度：拖拽时70px，静态时8px
    // - 定位圆点半径：拖拽时3px，静态时2px
    // - 从文本框底部到圆点中心的距离：连接线高度 + 圆点半径

    final connectionLineHeight = 70.0; // 拖拽时的连接线高度
    final dotRadius = 3.0; // 拖拽时的圆点半径
    final textBoxToDotOffset = connectionLineHeight + dotRadius;

    print(
      '🔄 [拖拽修复] 气泡结构: 连接线=${connectionLineHeight}px, 圆点半径=${dotRadius}px, 文本框到圆点偏移=${textBoxToDotOffset}px',
    );

    // 🔧 关键修复：计算定位圆点的全局位置
    final dotGlobalPosition = Offset(
      textBoxGlobalPosition.dx, // X坐标保持一致（水平居中）
      textBoxGlobalPosition.dy + textBoxToDotOffset, // Y坐标向下偏移
    );

    print(
      '🔄 [拖拽修复] 计算的定位圆点全局位置: ${dotGlobalPosition.dx.toStringAsFixed(1)}, ${dotGlobalPosition.dy.toStringAsFixed(1)}',
    );

    // 🔧 关键修复：将定位圆点的全局坐标转换为图片内坐标
    final newImagePosition = _convertGlobalToImagePosition(dotGlobalPosition);
    if (newImagePosition == null) return;

    setState(() {
      _tapPosition = Vector3(newImagePosition.dx, newImagePosition.dy, 0);
    });

    print(
      '🔄 [拖拽修复] 更新 _tapPosition: ${newImagePosition.dx.toStringAsFixed(1)}, ${newImagePosition.dy.toStringAsFixed(1)}',
    );
    print('🔄 [拖拽修复] ===== 拖拽位置更新完成 =====');

    // 🎯 验证拖拽更新时的圆点对齐
    _validateDotAlignment('拖拽更新');

    // 🔍 显示修复后的位置对比信息
    _displayDotPositionComparison(
      textBoxGlobalPosition,
      dotGlobalPosition, // 使用计算出的圆点位置
      newImagePosition,
    );
  }

  /// 🔍 显示蓝色拖动气泡和临时气泡的定位圆点位置对比
  void _displayDotPositionComparison(
    Offset textBoxPosition,
    Offset dragDotGlobalPosition,
    Offset finalImagePosition,
  ) {
    print('🔍 [圆点位置对比] ===== 蓝色拖动气泡 vs 临时气泡 =====');

    // 1. 蓝色拖动气泡信息
    print(
      '🔵 [蓝色拖动气泡] 文本框全局位置: ${textBoxPosition.dx.toStringAsFixed(1)}, ${textBoxPosition.dy.toStringAsFixed(1)}',
    );
    print(
      '🔵 [蓝色拖动气泡] 定位圆点全局位置: ${dragDotGlobalPosition.dx.toStringAsFixed(1)}, ${dragDotGlobalPosition.dy.toStringAsFixed(1)}',
    );

    // 2. 临时气泡信息（基于最终图片坐标）
    if (_tapPosition != null) {
      final tempBubbleImagePosition = Offset(_tapPosition!.x, _tapPosition!.y);
      print(
        '🟡 [临时气泡] 图片内坐标: ${tempBubbleImagePosition.dx.toStringAsFixed(1)}, ${tempBubbleImagePosition.dy.toStringAsFixed(1)}',
      );

      // 🔍 临时气泡的定位圆点位置分析
      // 注：临时气泡使用FractionalTranslation显示，其全局位置由Flutter自动计算
      print('🟡 [临时气泡] 使用FractionalTranslation自动定位，无需手动计算全局坐标');

      // 3. 通过图片坐标差异分析对齐情况
      final imagePositionDiff = Offset(
        (finalImagePosition.dx - tempBubbleImagePosition.dx).abs(),
        (finalImagePosition.dy - tempBubbleImagePosition.dy).abs(),
      );

      print(
        '📏 [图片坐标差异] X=${imagePositionDiff.dx.toStringAsFixed(1)}px, Y=${imagePositionDiff.dy.toStringAsFixed(1)}px',
      );

      if (imagePositionDiff.dx < 1.0 && imagePositionDiff.dy < 1.0) {
        print('📏 [图片坐标差异] ✅ 蓝色拖动气泡与临时气泡定位圆点基本对齐（差异<1px）');
      } else {
        print('📏 [图片坐标差异] ❌ 蓝色拖动气泡与临时气泡定位圆点存在明显偏移（差异≥1px）');
        print('📏 [图片坐标差异] ⚠️ 需要检查拖拽计算与显示逻辑的一致性');

        // 提供详细的偏移分析
        if (imagePositionDiff.dy > imagePositionDiff.dx) {
          print('📏 [偏移分析] 主要偏移方向：垂直方向，可能是气泡高度计算问题');
        } else {
          print('📏 [偏移分析] 主要偏移方向：水平方向，可能是水平对齐问题');
        }
      }
    }

    // 4. 图片坐标对比
    print(
      '🖼️ [图片坐标] 拖拽计算结果: ${finalImagePosition.dx.toStringAsFixed(1)}, ${finalImagePosition.dy.toStringAsFixed(1)}',
    );
    if (_tapPosition != null) {
      final currentImagePosition = Offset(_tapPosition!.x, _tapPosition!.y);
      final imagePositionDiff = Offset(
        (finalImagePosition.dx - currentImagePosition.dx).abs(),
        (finalImagePosition.dy - currentImagePosition.dy).abs(),
      );
      print(
        '🖼️ [图片坐标] 当前显示位置: ${currentImagePosition.dx.toStringAsFixed(1)}, ${currentImagePosition.dy.toStringAsFixed(1)}',
      );
      print(
        '🖼️ [图片坐标] 坐标差异: X=${imagePositionDiff.dx.toStringAsFixed(1)}px, Y=${imagePositionDiff.dy.toStringAsFixed(1)}px',
      );
    }

    print('🔍 [圆点位置对比] ==========================================');
  }

  /// 临时气泡拖拽系统 - 结束拖拽
  void _onTempBubbleDragEnd() {
    // 🎯 验证拖拽结束时的圆点对齐
    _validateDotAlignment('拖拽结束');

    // 🔧 横屏状态验证
    _validateLandscapeOrientation('拖拽结束');

    // 触觉反馈
    HapticFeedback.mediumImpact();

    setState(() {
      _isTempBubbleDragging = false;
    });

    // 拖拽结束后自动聚焦到输入框，方便用户继续输入
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted && _isPositionSelectionMode) {
        FocusScope.of(context).requestFocus(_knowledgeFocusNode);
      }
    });
  }

  /// 将全局坐标转换为图片内坐标（用于临时气泡）- 修复横屏状态下的位置跳变问题
  Offset? _convertGlobalToImagePosition(Offset globalPosition) {
    try {
      final sizeInfo = _imageSizeInfos[_currentPageIndex];
      if (sizeInfo == null) return null;

      final currentController = _transformControllers[_currentPageIndex];
      if (currentController == null) return null;

      final transform = currentController.value;
      final scale = transform.getMaxScaleOnAxis();
      final translation = transform.getTranslation();

      // 🔧 横屏状态检测和特殊处理
      final orientation = MediaQuery.of(context).orientation;
      final isLandscape = orientation == Orientation.landscape;

      // 🎯 关键修复：直接转换为图片内坐标，与临时气泡定位逻辑保持一致
      double imageX = (globalPosition.dx - translation.x) / scale;
      double imageY = (globalPosition.dy - translation.y) / scale;

      // 🔍 横屏状态下的坐标转换调试
      if (isLandscape) {
        print(
          '🔄 [横屏坐标转换] 全局坐标: ${globalPosition.dx.toStringAsFixed(1)}, ${globalPosition.dy.toStringAsFixed(1)}',
        );
        print(
          '🔄 [横屏坐标转换] 变换参数: 缩放=${scale.toStringAsFixed(3)}, 平移=${translation.x.toStringAsFixed(1)}, ${translation.y.toStringAsFixed(1)}',
        );
        print(
          '🔄 [横屏坐标转换] 转换前图片坐标: ${imageX.toStringAsFixed(1)}, ${imageY.toStringAsFixed(1)}',
        );
        print(
          '🔄 [横屏坐标转换] 图片尺寸: ${sizeInfo.currentSize.width.toStringAsFixed(1)} x ${sizeInfo.currentSize.height.toStringAsFixed(1)}',
        );
      }

      // 边界限制 - 确保在图片范围内
      final clampedX = imageX.clamp(0.0, sizeInfo.currentSize.width);
      final clampedY = imageY.clamp(0.0, sizeInfo.currentSize.height);

      // 🔍 横屏状态下的边界限制调试
      if (isLandscape) {
        print(
          '🔄 [横屏坐标转换] 边界限制后: ${clampedX.toStringAsFixed(1)}, ${clampedY.toStringAsFixed(1)}',
        );

        // 检查是否发生了显著的坐标变化（可能导致跳变）
        final deltaX = (clampedX - imageX).abs();
        final deltaY = (clampedY - imageY).abs();
        if (deltaX > 10 || deltaY > 10) {
          print(
            '🔄 [横屏警告] 检测到显著的坐标变化，可能导致跳变: ΔX=${deltaX.toStringAsFixed(1)}, ΔY=${deltaY.toStringAsFixed(1)}',
          );
        }
      }

      return Offset(clampedX, clampedY);
    } catch (e) {
      print('🔄 [横屏错误] 坐标转换失败: $e');
      return null;
    }
  }

  /// 初始化单个图片的变换矩阵 - 使用标准化坐标系统
  Future<void> _initializeImageMatrix(int index) async {
    if (!mounted) return;

    final screenSize = MediaQuery.of(context).size;
    final imagePath = _imageList[index];

    // 获取完整的图片尺寸信息（包括原始尺寸和当前尺寸）
    final sizeInfo = await coord.ImageCoordinateSystem.getImageSizeInfo(
      imagePath,
    );
    if (!mounted || sizeInfo == null) return;

    // 缓存图片尺寸信息
    _imageSizeInfos[index] = sizeInfo;
    _imageSizes[index] = sizeInfo.currentSize; // 保持向后兼容

    final imageSize = sizeInfo.currentSize;

    print('🎯 [标准化坐标] 图片$index: ${sizeInfo.toString()}');

    // 计算缩放比例
    final scaleX = screenSize.width / imageSize.width;
    final scaleY = screenSize.height / imageSize.height;

    // 计算 Contain 模式（完整显示）的矩阵
    final containScale = scaleX < scaleY ? scaleX : scaleY;
    final translationX =
        (screenSize.width - imageSize.width * containScale) / 2;
    final translationY =
        (screenSize.height - imageSize.height * containScale) / 2;

    final containMatrix = Matrix4.identity()
      ..translate(translationX, translationY)
      ..scale(containScale);

    // 🔍 详细分析不同尺寸照片的平移量
    String photoType = "截图照片";
    if (imageSize.width < imageSize.height) {
      photoType = "竖屏照片";
    } else if (imageSize.width > imageSize.height) {
      photoType = "横屏照片";
    }

    print('📐 [平移量分析-$photoType] ===== 开始分析 =====');
    print(
      '📐 [平移量分析-$photoType] 图片尺寸: ${imageSize.width.toInt()}x${imageSize.height.toInt()}',
    );
    print(
      '📐 [平移量分析-$photoType] 屏幕尺寸: ${screenSize.width.toInt()}x${screenSize.height.toInt()}',
    );
    print(
      '📐 [平移量分析-$photoType] 缩放比例: scaleX=${scaleX.toStringAsFixed(3)}, scaleY=${scaleY.toStringAsFixed(3)}',
    );
    print(
      '📐 [平移量分析-$photoType] 选择的缩放: ${containScale.toStringAsFixed(3)} (${containScale == scaleX ? "scaleX" : "scaleY"})',
    );
    print(
      '📐 [平移量分析-$photoType] 计算的平移量: (${translationX.toStringAsFixed(1)}, ${translationY.toStringAsFixed(1)})',
    );

    // 分析平移量的意义
    if (translationX.abs() < 1.0 && translationY.abs() < 1.0) {
      print('📐 [平移量分析-$photoType] ✅ 几乎无平移，图片接近屏幕比例');
    } else if (translationX.abs() > translationY.abs()) {
      print(
        '📐 [平移量分析-$photoType] ➡️ 主要是水平平移 ${translationX.toStringAsFixed(1)}px，图片较窄',
      );
    } else {
      print(
        '📐 [平移量分析-$photoType] ⬆️ 主要是垂直平移 ${translationY.toStringAsFixed(1)}px，图片较矮',
      );
    }
    print('📐 [平移量分析-$photoType] ===== 分析结束 =====');

    // 计算 Cover 模式（占满屏幕）的矩阵 - 改为左上角对齐
    final coverScale = scaleX > scaleY ? scaleX : scaleY;
    final coverMatrix = Matrix4.identity()
      ..translate(0.0, 0.0) // 从左上角开始，让用户自然地平移查看完整内容
      ..scale(coverScale);

    // 更新状态
    setState(() {
      _imageSizes[index] = imageSize;
      _containMatrices[index] = containMatrix;
      _coverMatrices[index] = coverMatrix;

      // 设置初始显示模式为Contain（全局预览）
      _imageCoverModes[index] = false;

      // 设置初始视图为Contain模式（全局预览）
      _transformControllers[index]?.value = containMatrix;
    });

    print(
      '🎯 [矩阵初始化] 图片$index: 原始=${imageSize.width}x${imageSize.height}, Contain缩放=${containScale.toStringAsFixed(2)}, Cover缩放=${coverScale.toStringAsFixed(2)}',
    );
    print('🎯 [矩阵初始化] Cover模式采用左上角对齐，用户可平移查看完整内容');
    print('🔍 [矩阵调试] Contain矩阵: ${containMatrix.toString()}');
    print('🔍 [矩阵调试] Cover矩阵: ${coverMatrix.toString()}');
  }
}

/// 锚点类型枚举
enum AnchorType {
  motivation, // 激励
  goal, // 目标
  location, // 位置
  future, // 未来
  dream, // 梦想
  study, // 学习
}

/// 记忆锚点数据模型
class MemoryAnchor {
  final String id;
  double xRatio; // X坐标比例 (0.0-1.0)
  double yRatio; // Y坐标比例 (0.0-1.0)
  String content; // 改为可变，支持编辑
  final String? authorName;
  int likes;
  final DateTime createdAt;
  final AnchorType type;

  MemoryAnchor({
    required this.id,
    required this.xRatio,
    required this.yRatio,
    required this.content,
    this.authorName,
    required this.likes,
    required this.createdAt,
    required this.type,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'xRatio': xRatio,
      'yRatio': yRatio,
      'content': content,
      'authorName': authorName,
      'likes': likes,
      'createdAt': createdAt.toIso8601String(),
      'type': type.index,
    };
  }

  /// 从JSON创建对象
  static MemoryAnchor fromJson(Map<String, dynamic> json) {
    return MemoryAnchor(
      id: json['id'] as String,
      xRatio: (json['xRatio'] as num).toDouble(),
      yRatio: (json['yRatio'] as num).toDouble(),
      content: json['content'] as String,
      authorName: json['authorName'] as String?,
      likes: json['likes'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      type: AnchorType.values[json['type'] as int],
    );
  }
}

/// 临时位置选择气泡组件 - 支持拖拽
class _TempPositionBubble extends StatefulWidget {
  final bool isDragging;
  final VoidCallback? onDragStart;
  final Function(Offset)? onDragUpdate;
  final VoidCallback? onDragEnd;

  const _TempPositionBubble({
    this.isDragging = false,
    this.onDragStart,
    this.onDragUpdate,
    this.onDragEnd,
  });

  @override
  State<_TempPositionBubble> createState() => _TempPositionBubbleState();
}

class _TempPositionBubbleState extends State<_TempPositionBubble> {
  // 🔧 横屏修复：记录拖拽开始时的偏移量
  Offset? _dragStartOffset;
  final GlobalKey _textBoxKey = GlobalKey();

  // 🔍 拖拽平滑性验证变量
  Offset? _lastFingerPosition;
  Offset? _lastTextBoxPosition;

  @override
  Widget build(BuildContext context) {
    Widget bubbleWidget = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 🎯 重新设计：将手势检测区域限制在文本框部分
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onLongPressStart: (details) {
            // 🔧 关键修复：使用理论位置而非实际渲染位置，避免位置跳变
            //
            // 问题分析：
            // 1. RenderBox.localToGlobal 返回的是实际渲染位置
            // 2. 由于 FractionalTranslation 和复杂的变换，实际位置可能与理论位置不一致
            // 3. 这导致第一次拖拽移动时出现位置跳变
            //
            // 解决方案：
            // 1. 使用临时气泡的理论基准位置（_tapPosition）
            // 2. 计算手指相对于理论位置的偏移量
            // 3. 确保拖拽过程中的位置连续性

            print('🔄 [拖拽修复] ===== 拖拽开始分析 =====');
            print(
              '🔄 [拖拽修复] 手指位置: ${details.globalPosition.dx.toStringAsFixed(1)}, ${details.globalPosition.dy.toStringAsFixed(1)}',
            );

            // 方法1：尝试使用RenderBox获取实际位置（用于对比）
            final context = _textBoxKey.currentContext;
            Offset? actualTextBoxPosition;
            if (context != null) {
              final renderBox = context.findRenderObject() as RenderBox?;
              if (renderBox != null) {
                actualTextBoxPosition = renderBox.localToGlobal(Offset.zero);
                print(
                  '🔄 [拖拽修复] 文本框实际位置: ${actualTextBoxPosition.dx.toStringAsFixed(1)}, ${actualTextBoxPosition.dy.toStringAsFixed(1)}',
                );
              }
            }

            // 方法2：使用理论位置计算偏移量（推荐方案）
            // 假设文本框位置就是手指当前位置，这样可以避免位置跳变
            _dragStartOffset = Offset.zero;

            print('🔄 [拖拽修复] 使用零偏移量策略，避免位置跳变');
            print(
              '🔄 [拖拽修复] 设置偏移量: ${_dragStartOffset!.dx.toStringAsFixed(1)}, ${_dragStartOffset!.dy.toStringAsFixed(1)}',
            );

            if (actualTextBoxPosition != null) {
              final positionDiff = Offset(
                (details.globalPosition.dx - actualTextBoxPosition.dx).abs(),
                (details.globalPosition.dy - actualTextBoxPosition.dy).abs(),
              );
              print(
                '🔄 [拖拽修复] 手指与实际文本框位置差异: ${positionDiff.dx.toStringAsFixed(1)}, ${positionDiff.dy.toStringAsFixed(1)}',
              );

              if (positionDiff.dx > 5.0 || positionDiff.dy > 5.0) {
                print('🔄 [拖拽修复] ⚠️ 位置差异较大，可能存在跳变风险');
              } else {
                print('🔄 [拖拽修复] ✅ 位置差异较小，拖拽应该平滑');
              }
            }

            print('🔄 [拖拽修复] ===== 拖拽开始分析结束 =====');

            widget.onDragStart?.call();
          },
          onLongPressMoveUpdate: (details) {
            // 🔧 关键修复：使用零偏移量策略，确保拖拽平滑无跳变
            //
            // 由于我们在 onLongPressStart 中设置了零偏移量，
            // 这里直接使用手指位置作为文本框位置，确保拖拽的连续性

            final textBoxGlobalPosition =
                details.globalPosition - _dragStartOffset!;

            // 🔍 拖拽平滑性验证
            if (_lastFingerPosition != null && _lastTextBoxPosition != null) {
              final fingerMovement = Offset(
                details.globalPosition.dx - _lastFingerPosition!.dx,
                details.globalPosition.dy - _lastFingerPosition!.dy,
              );
              final textBoxMovement = Offset(
                textBoxGlobalPosition.dx - _lastTextBoxPosition!.dx,
                textBoxGlobalPosition.dy - _lastTextBoxPosition!.dy,
              );

              // 验证移动比例是否一致（应该接近1:1）
              final movementRatioX = fingerMovement.dx.abs() > 0.1
                  ? textBoxMovement.dx / fingerMovement.dx
                  : 1.0;
              final movementRatioY = fingerMovement.dy.abs() > 0.1
                  ? textBoxMovement.dy / fingerMovement.dy
                  : 1.0;

              if ((movementRatioX - 1.0).abs() > 0.1 ||
                  (movementRatioY - 1.0).abs() > 0.1) {
                print(
                  '🔄 [拖拽平滑性] ⚠️ 移动比例异常: X=${movementRatioX.toStringAsFixed(2)}, Y=${movementRatioY.toStringAsFixed(2)}',
                );
              }
            }

            // 🎯 验证用户体验要求：手指位置应与文本框位置一致
            final fingerToTextBoxDistance =
                (details.globalPosition - textBoxGlobalPosition).distance;

            print(
              '🔄 [拖拽修复] 移动中 - 手指位置: ${details.globalPosition.dx.toStringAsFixed(1)}, ${details.globalPosition.dy.toStringAsFixed(1)}',
            );
            print(
              '🔄 [拖拽修复] 移动中 - 偏移量: ${_dragStartOffset!.dx.toStringAsFixed(1)}, ${_dragStartOffset!.dy.toStringAsFixed(1)}',
            );
            print(
              '🔄 [拖拽修复] 移动中 - 计算的文本框位置: ${textBoxGlobalPosition.dx.toStringAsFixed(1)}, ${textBoxGlobalPosition.dy.toStringAsFixed(1)}',
            );
            print(
              '🎯 [用户体验验证] 手指与文本框距离: ${fingerToTextBoxDistance.toStringAsFixed(1)}px ${fingerToTextBoxDistance < 1.0 ? "✅ 完美对齐" : "⚠️ 存在偏差"}',
            );

            // 🔍 定位圆点位置计算（用于验证用户能否清楚看到圆点）
            // 定位圆点相对于文本框的偏移：文本框下方 + 连接线长度 + 圆点半径
            final connectionLineHeight = widget.isDragging ? 70.0 : 8.0;
            final dotRadius = widget.isDragging ? 3.0 : 2.0;
            final dotOffset = connectionLineHeight + dotRadius;

            final estimatedDotPosition = Offset(
              textBoxGlobalPosition.dx,
              textBoxGlobalPosition.dy + dotOffset,
            );

            final fingerToDotDistance =
                (details.globalPosition - estimatedDotPosition).distance;

            print(
              '🎯 [定位圆点验证] 预估圆点位置: ${estimatedDotPosition.dx.toStringAsFixed(1)}, ${estimatedDotPosition.dy.toStringAsFixed(1)}',
            );
            print(
              '🎯 [定位圆点验证] 手指与圆点距离: ${fingerToDotDistance.toStringAsFixed(1)}px ${fingerToDotDistance > 50.0 ? "✅ 圆点清晰可见" : "⚠️ 可能被手指遮挡"}',
            );

            // 更新历史位置用于下次验证
            _lastFingerPosition = details.globalPosition;
            _lastTextBoxPosition = textBoxGlobalPosition;

            widget.onDragUpdate?.call(textBoxGlobalPosition);
          },
          onLongPressEnd: (details) {
            // 🔧 拖拽结束清理：重置所有拖拽相关状态
            print('🔄 [拖拽修复] ===== 拖拽结束清理 =====');
            print('🔄 [拖拽修复] 清理偏移量和历史位置记录');

            _dragStartOffset = null;
            _lastFingerPosition = null;
            _lastTextBoxPosition = null;

            print('🔄 [拖拽修复] ===== 拖拽结束清理完成 =====');

            widget.onDragEnd?.call();
          },
          child: Container(
            key: _textBoxKey, // 🔧 横屏修复：添加key用于获取位置
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(
                    alpha: widget.isDragging ? 0.15 : 0.1,
                  ),
                  blurRadius: widget.isDragging ? 8 : 6,
                  offset: Offset(0, widget.isDragging ? 3 : 2),
                ),
              ],
              border: Border.all(
                color: widget.isDragging
                    ? Colors.blue.shade300
                    : const Color(0xFF2E7EED),
                width: widget.isDragging ? 1.5 : 1,
              ),
              // 🎯 添加拖拽提示的视觉反馈
              gradient: widget.isDragging
                  ? LinearGradient(
                      colors: [Colors.blue.shade50, Colors.white],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    )
                  : null,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 🎯 添加拖拽图标提示
                if (widget.isDragging)
                  Padding(
                    padding: const EdgeInsets.only(right: 4),
                    child: Icon(
                      Icons.drag_indicator,
                      size: 12,
                      color: Colors.blue.shade600,
                    ),
                  ),
                Text(
                  widget.isDragging ? '拖拽调整位置' : '长按文本框拖拽',
                  style: TextStyle(
                    fontSize: 11,
                    color: widget.isDragging
                        ? Colors.blue.shade700
                        : const Color(0xFF37352F),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),

        // 指针连线 - 拖拽时变色变粗并加长，避免手指遮挡
        Container(
          width: widget.isDragging ? 2.5 : 1.5,
          height: widget.isDragging ? 70 : 8, // 拖拽时加长到70px，确保手指不遮挡定位圆点
          decoration: BoxDecoration(
            color: widget.isDragging ? Colors.blue.shade300 : Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: widget.isDragging ? 0.15 : 0.1,
                ),
                blurRadius: widget.isDragging ? 2 : 1,
                offset: const Offset(0, 0.5),
              ),
            ],
          ),
        ),

        // 🎯 定位圆点 - 拖拽时变大变蓝，确保与KnowledgePointBubble的圆点尺寸完全一致
        Container(
          width: widget.isDragging ? 6 : 4,
          height: widget.isDragging ? 6 : 4,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(
              color: widget.isDragging ? Colors.blue : Colors.red,
              width: widget.isDragging ? 1.5 : 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: widget.isDragging ? 0.25 : 0.15,
                ),
                blurRadius: widget.isDragging ? 6 : 3,
                offset: Offset(0, widget.isDragging ? 2 : 1),
              ),
            ],
          ),
          // 🔍 调试标记：临时气泡的定位圆点
          child: widget.isDragging
              ? Center(
                  child: Container(
                    width: 1,
                    height: 1,
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                  ),
                )
              : null,
        ),
      ],
    );

    // 添加拖拽时的放大效果
    if (widget.isDragging) {
      bubbleWidget = Transform.scale(
        scale: 1.1,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: bubbleWidget,
        ),
      );
    }

    // 🎯 移除整体手势检测，现在只有文本框部分可以拖拽
    // 这样可以确保用户拖拽文本框时，手指不会遮挡定位圆点
    return bubbleWidget;
  }
}

/// 相关场景数据模型
class RelatedScene {
  final String id;
  final String title;
  final String imagePath;
  final bool isSelected;

  RelatedScene({
    required this.id,
    required this.title,
    required this.imagePath,
    required this.isSelected,
  });
}

/// 知识点气泡组件 - 支持直接拖拽的新架构
class KnowledgePointBubble extends StatefulWidget {
  final MemoryAnchor anchor;
  final bool isSelected;
  final bool isEditMode;
  final bool isDragging;
  final Function(MemoryAnchor, Offset)? onRepositionStart;
  final Function(MemoryAnchor, Offset)? onRepositionUpdate;
  final Function(MemoryAnchor)? onRepositionEnd;
  final VoidCallback? onTap;

  const KnowledgePointBubble({
    super.key,
    required this.anchor,
    this.isSelected = false,
    this.isEditMode = false,
    this.isDragging = false,
    this.onRepositionStart,
    this.onRepositionUpdate,
    this.onRepositionEnd,
    this.onTap,
  });

  @override
  State<KnowledgePointBubble> createState() => _KnowledgePointBubbleState();
}

class _KnowledgePointBubbleState extends State<KnowledgePointBubble> {
  @override
  Widget build(BuildContext context) {
    Widget bubbleWidget = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 紧凑文本框 - 拖拽时高亮显示
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: widget.isDragging ? 0.15 : 0.1,
                ),
                blurRadius: widget.isDragging ? 8 : 6,
                offset: Offset(0, widget.isDragging ? 3 : 2),
              ),
            ],
            border: Border.all(
              color: widget.isDragging
                  ? Colors.blue.shade300
                  : const Color(0xFF37352F).withValues(alpha: 0.1),
              width: widget.isDragging ? 1.5 : 1,
            ),
          ),
          child: Text(
            widget.anchor.content,
            style: TextStyle(
              fontSize: 11,
              color: widget.isDragging
                  ? Colors.blue.shade700
                  : const Color(0xFF37352F),
              fontWeight: FontWeight.w500,
              height: 1.2,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),

        // 指针连线 - 从文本框底部延伸的指针，拖拽时变色变粗并加长
        Container(
          width: widget.isDragging ? 2.5 : 1.5,
          height: widget.isDragging ? 70 : 8, // 拖拽时加长到70px，确保手指不遮挡定位圆点
          decoration: BoxDecoration(
            color: widget.isDragging ? Colors.blue.shade300 : Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: widget.isDragging ? 0.15 : 0.1,
                ),
                blurRadius: widget.isDragging ? 2 : 1,
                offset: const Offset(0, 0.5),
              ),
            ],
          ),
        ),

        // 🎯 定位圆点 - 指针末端的纯白色圆点，确保与临时气泡的圆点尺寸完全一致
        Container(
          width: widget.isDragging ? 6 : 4,
          height: widget.isDragging ? 6 : 4,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(
              color: widget.isEditMode
                  ? (widget.isDragging ? Colors.blue : Colors.blue.shade300)
                  : Colors.grey.shade400,
              width: widget.isEditMode ? (widget.isDragging ? 1.5 : 1.0) : 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(
                  alpha: widget.isDragging ? 0.25 : 0.15,
                ),
                blurRadius: widget.isDragging ? 6 : 3,
                offset: Offset(0, widget.isDragging ? 2 : 1),
              ),
            ],
          ),
          // 🔍 调试标记和编辑模式指示器
          child: widget.isDragging
              ? Center(
                  child: Container(
                    width: 1,
                    height: 1,
                    decoration: const BoxDecoration(
                      color: Colors.green, // 绿色标记表示锚点气泡的定位圆点
                      shape: BoxShape.circle,
                    ),
                  ),
                )
              : (widget.isEditMode
                    ? Center(
                        child: Container(
                          width: 1.5,
                          height: 1.5,
                          decoration: const BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                          ),
                        ),
                      )
                    : null),
        ),
      ],
    );

    // 如果是编辑模式，添加拖拽手势和视觉反馈
    if (widget.isEditMode) {
      // 添加拖拽时的视觉反馈
      if (widget.isDragging) {
        bubbleWidget = Transform.scale(
          scale: 1.1,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: bubbleWidget,
          ),
        );
      }

      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onPanStart: (details) {
          print('🎯 [KnowledgePointBubble] 开始拖拽: ${widget.anchor.id}');
          print(
            '🎯 [KnowledgePointBubble] 拖拽开始位置: ${details.globalPosition.dx.toStringAsFixed(1)}, ${details.globalPosition.dy.toStringAsFixed(1)}',
          );
          widget.onRepositionStart?.call(widget.anchor, details.globalPosition);
        },
        onPanUpdate: (details) {
          print('🎯 [KnowledgePointBubble] 拖拽更新: ${details.globalPosition}');
          widget.onRepositionUpdate?.call(
            widget.anchor,
            details.globalPosition,
          );
        },
        onPanEnd: (details) {
          print('🎯 [KnowledgePointBubble] 拖拽结束: ${widget.anchor.id}');
          widget.onRepositionEnd?.call(widget.anchor);
        },
        child: bubbleWidget,
      );
    }

    // 如果有点击回调，使用GestureDetector包装
    if (widget.onTap != null) {
      return GestureDetector(onTap: widget.onTap, child: bubbleWidget);
    }

    return bubbleWidget;
  }
}

/// 自定义快速响应滚动物理效果 - 优化快速滑动体验
class _FastResponseScrollPhysics extends ScrollPhysics {
  const _FastResponseScrollPhysics({super.parent});

  @override
  _FastResponseScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return _FastResponseScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  double get minFlingVelocity => 50.0; // 降低最小滑动速度阈值

  @override
  double get maxFlingVelocity => 8000.0; // 提高最大滑动速度

  @override
  double get dragStartDistanceMotionThreshold => 3.0; // 降低拖拽开始距离阈值

  @override
  SpringDescription get spring => const SpringDescription(
    mass: 0.5, // 降低质量，提升响应性
    stiffness: 500.0, // 提高刚度，加快响应
    damping: 30.0, // 适中的阻尼，保持流畅
  );

  @override
  Tolerance get tolerance => const Tolerance(
    velocity: 0.02, // 降低速度容差
    distance: 0.001, // 降低距离容差
  );

  @override
  double carriedMomentum(double existingVelocity) {
    // 保持更多的动量，让快速滑动更流畅
    return existingVelocity * 0.95;
  }

  // 移除了 frictionFactor 方法，因为它不是 ScrollPhysics 的有效重写方法
}

/// 场景卡片组件
class SceneCard extends StatelessWidget {
  final RelatedScene scene;
  final VoidCallback onTap;

  const SceneCard({super.key, required this.scene, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: 80,
        child: Column(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: scene.isSelected
                    ? Border.all(color: Colors.white, width: 2)
                    : null,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child:
                    scene.imagePath.startsWith('http://') ||
                        scene.imagePath.startsWith('https://')
                    ? Image.network(
                        scene.imagePath,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey.withValues(alpha: 0.3),
                            child: const Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 24,
                            ),
                          );
                        },
                      )
                    : Image.file(
                        File(scene.imagePath),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey.withValues(alpha: 0.3),
                            child: const Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 24,
                            ),
                          );
                        },
                      ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              scene.title,
              style: TextStyle(
                color: scene.isSelected
                    ? const Color(0xFF2E7EED)
                    : const Color(0xFF37352F).withValues(alpha: 0.8),
                fontSize: 12,
                fontWeight: scene.isSelected
                    ? FontWeight.w600
                    : FontWeight.w400,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
