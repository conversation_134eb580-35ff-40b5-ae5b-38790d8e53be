import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../community/article_import_service.dart';
import '../community/profanity_filter_settings_page.dart';
import '../../services/locale_service.dart';
import '../../l10n/app_localizations.dart';

/// 设置页面
///
/// 提供系统配置、数据管理、隐私设置等功能
/// 采用Notion风格，简洁清晰的分组布局
class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  // 设置状态
  bool _notificationsEnabled = true;
  bool _autoBackup = true;
  bool _biometricEnabled = false;
  bool _analyticsEnabled = true;
  // Note: Theme and font size settings reserved for future implementation
  // String _selectedTheme = 'system';
  // double _fontSize = 16.0;

  // 词汇高亮设置
  bool _vocabularyHighlightEnabled = true;
  bool _expertOnlyHighlight = false;

  // 应用信息
  String _appVersion = '';
  String _buildNumber = '';

  // 文章导入服务
  final ArticleImportService _importService = ArticleImportService();

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadAppInfo();
  }

  /// 加载设置数据
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // 加载词汇高亮设置
    final highlightSettings = await _importService.getHighlightSettings();

    setState(() {
      _notificationsEnabled = prefs.getBool('notifications') ?? true;
      _autoBackup = prefs.getBool('auto_backup') ?? true;
      _biometricEnabled = prefs.getBool('biometric') ?? false;
      _analyticsEnabled = prefs.getBool('analytics') ?? true;
      // Note: Theme and font size loading commented out for future implementation
      // _selectedTheme = prefs.getString('theme') ?? 'system';
      // _fontSize = prefs.getDouble('font_size') ?? 16.0;

      // 词汇高亮设置
      _vocabularyHighlightEnabled = highlightSettings.enabled;
      _expertOnlyHighlight = highlightSettings.expertOnly;
    });
  }

  /// 加载应用信息
  Future<void> _loadAppInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = packageInfo.version;
      _buildNumber = packageInfo.buildNumber;
    });
  }

  /// 保存设置
  Future<void> _saveSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();
    if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is String) {
      await prefs.setString(key, value);
    } else if (value is double) {
      await prefs.setDouble(key, value);
    }
  }

  /// 保存词汇高亮设置
  Future<void> _saveVocabularyHighlightSettings() async {
    try {
      final settings = HighlightSettings(
        enabled: _vocabularyHighlightEnabled,
        expertOnly: _expertOnlyHighlight,
      );
      await _importService.saveHighlightSettings(settings);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('词汇高亮设置已保存'),
            backgroundColor: Color(0xFF0F7B6C),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存设置失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF37352F)),
        ),
        title: Text(
          l10n.settings,
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 系统偏好
            _buildSettingsSection(
              title: l10n.systemPreferences,
              children: [
                _buildLanguageItem(),
                _buildDivider(),
                _buildNotificationItem(),
                _buildDivider(),
                _buildVocabularyHighlightItem(),
                _buildDivider(),
                _buildExpertOnlyItem(),
                _buildDivider(),
                _buildContentFilterItem(),
              ],
            ),

            const SizedBox(height: 24),

            // 数据管理
            _buildSettingsSection(
              title: l10n.dataManagement,
              children: [
                _buildAutoBackupItem(),
                _buildDivider(),
                _buildExportDataItem(),
                _buildDivider(),
                _buildClearCacheItem(),
                _buildDivider(),
                _buildResetDataItem(),
              ],
            ),

            const SizedBox(height: 24),

            // 隐私安全
            _buildSettingsSection(
              title: l10n.privacySecurity,
              children: [
                _buildBiometricItem(),
                _buildDivider(),
                _buildAnalyticsItem(),
                _buildDivider(),
                _buildPrivacyPolicyItem(),
              ],
            ),

            const SizedBox(height: 24),

            // 关于应用
            _buildSettingsSection(
              title: l10n.aboutApp,
              children: [
                _buildVersionItem(),
                _buildDivider(),
                _buildUserAgreementItem(),
                _buildDivider(),
                _buildContactUsItem(),
                _buildDivider(),
                _buildRateAppItem(),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// 构建设置分组
  Widget _buildSettingsSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 12),

        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  /// 构建分割线
  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.only(left: 72),
      height: 1,
      color: const Color(0xFF37352F).withValues(alpha: 0.06),
    );
  }

  /// 语言设置项
  Widget _buildLanguageItem() {
    final l10n = AppLocalizations.of(context)!;
    final currentLocale = ref.watch(localeProvider);
    final currentLanguageName = LocaleService.getLanguageName(currentLocale);

    return Semantics(
      label: '${l10n.language}，当前选择：$currentLanguageName',
      child: _buildSettingsItem(
        icon: Icons.language_outlined,
        title: l10n.language,
        subtitle: currentLanguageName,
        iconColor: const Color(0xFF2E7EED),
        onTap: _showLanguageSelector,
      ),
    );
  }

  /// 通知设置项
  Widget _buildNotificationItem() {
    final l10n = AppLocalizations.of(context)!;

    return Semantics(
      label:
          '${l10n.notifications}，当前状态：${_notificationsEnabled ? "已开启" : "已关闭"}',
      child: _buildSwitchItem(
        icon: Icons.notifications_outlined,
        title: l10n.notifications,
        subtitle: l10n.notificationsDescription,
        iconColor: const Color(0xFFE03E3E),
        value: _notificationsEnabled,
        onChanged: (value) {
          setState(() {
            _notificationsEnabled = value;
          });
          _saveSetting('notifications', value);
        },
      ),
    );
  }

  /// 词汇高亮设置项
  Widget _buildVocabularyHighlightItem() {
    return Semantics(
      label: '词汇高亮，当前状态：${_vocabularyHighlightEnabled ? "已开启" : "已关闭"}',
      child: _buildSwitchItem(
        icon: Icons.highlight_outlined,
        title: '词汇高亮',
        subtitle: '在文章中高亮显示考研词汇',
        iconColor: const Color(0xFFD9730D),
        value: _vocabularyHighlightEnabled,
        onChanged: (value) {
          setState(() {
            _vocabularyHighlightEnabled = value;
          });
          _saveVocabularyHighlightSettings();
        },
      ),
    );
  }

  /// 专家级词汇设置项
  Widget _buildExpertOnlyItem() {
    return Semantics(
      label: '仅高亮专家级词汇，当前状态：${_expertOnlyHighlight ? "已开启" : "已关闭"}',
      child: _buildSwitchItem(
        icon: Icons.school_outlined,
        title: '仅高亮专家级词汇',
        subtitle: '只高亮expert级别的考研词汇',
        iconColor: const Color(0xFF7C3AED),
        value: _expertOnlyHighlight,
        onChanged: _vocabularyHighlightEnabled
            ? (value) {
                setState(() {
                  _expertOnlyHighlight = value;
                });
                _saveVocabularyHighlightSettings();
              }
            : null,
      ),
    );
  }

  /// 内容过滤设置项
  Widget _buildContentFilterItem() {
    return Semantics(
      label: '内容过滤设置',
      button: true,
      child: _buildSettingsItem(
        icon: Icons.shield_outlined,
        title: '内容过滤',
        subtitle: '管理脏话过滤和举报设置',
        iconColor: const Color(0xFFE03E3E),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ProfanityFilterSettingsPage(),
            ),
          );
        },
      ),
    );
  }

  /// 自动备份设置项
  Widget _buildAutoBackupItem() {
    return Semantics(
      label: '自动备份，当前状态：${_autoBackup ? "已开启" : "已关闭"}',
      child: _buildSwitchItem(
        icon: Icons.backup_outlined,
        title: '自动备份',
        subtitle: '定期备份学习数据到云端',
        iconColor: const Color(0xFF0F7B6C),
        value: _autoBackup,
        onChanged: (value) {
          setState(() {
            _autoBackup = value;
          });
          _saveSetting('auto_backup', value);
        },
      ),
    );
  }

  /// 导出数据设置项
  Widget _buildExportDataItem() {
    return Semantics(
      label: '导出数据',
      button: true,
      child: _buildSettingsItem(
        icon: Icons.download_outlined,
        title: '导出数据',
        subtitle: '导出学习记录和工资数据',
        iconColor: const Color(0xFF2E7EED),
        onTap: _exportData,
      ),
    );
  }

  /// 清理缓存设置项
  Widget _buildClearCacheItem() {
    return Semantics(
      label: '清理缓存',
      button: true,
      child: _buildSettingsItem(
        icon: Icons.cleaning_services_outlined,
        title: '清理缓存',
        subtitle: '清理临时文件释放存储空间',
        iconColor: const Color(0xFFD9730D),
        onTap: _clearCache,
      ),
    );
  }

  /// 重置数据设置项
  Widget _buildResetDataItem() {
    return Semantics(
      label: '重置数据',
      button: true,
      child: _buildSettingsItem(
        icon: Icons.restore_outlined,
        title: '重置数据',
        subtitle: '清除所有数据恢复初始状态',
        iconColor: const Color(0xFFE03E3E),
        onTap: _resetData,
      ),
    );
  }

  /// 生物识别设置项
  Widget _buildBiometricItem() {
    return Semantics(
      label: '生物识别解锁，当前状态：${_biometricEnabled ? "已开启" : "已关闭"}',
      child: _buildSwitchItem(
        icon: Icons.fingerprint_outlined,
        title: '生物识别解锁',
        subtitle: '使用指纹或面容解锁应用',
        iconColor: const Color(0xFF7C3AED),
        value: _biometricEnabled,
        onChanged: (value) {
          setState(() {
            _biometricEnabled = value;
          });
          _saveSetting('biometric', value);
        },
      ),
    );
  }

  /// 数据分析设置项
  Widget _buildAnalyticsItem() {
    return Semantics(
      label: '数据分析，当前状态：${_analyticsEnabled ? "已开启" : "已关闭"}',
      child: _buildSwitchItem(
        icon: Icons.analytics_outlined,
        title: '数据分析',
        subtitle: '帮助改善应用体验',
        iconColor: const Color(0xFF0F7B6C),
        value: _analyticsEnabled,
        onChanged: (value) {
          setState(() {
            _analyticsEnabled = value;
          });
          _saveSetting('analytics', value);
        },
      ),
    );
  }

  /// 隐私政策设置项
  Widget _buildPrivacyPolicyItem() {
    return Semantics(
      label: '隐私政策',
      button: true,
      child: _buildSettingsItem(
        icon: Icons.privacy_tip_outlined,
        title: '隐私政策',
        subtitle: '了解我们如何保护您的隐私',
        iconColor: const Color(0xFF9B9A97),
        onTap: _showPrivacyPolicy,
      ),
    );
  }

  /// 版本信息设置项
  Widget _buildVersionItem() {
    return Semantics(
      label: '版本信息：$_appVersion',
      child: _buildSettingsItem(
        icon: Icons.info_outline,
        title: '版本',
        subtitle: '$_appVersion ($_buildNumber)',
        iconColor: const Color(0xFF2E7EED),
        showArrow: false,
      ),
    );
  }

  /// 用户协议设置项
  Widget _buildUserAgreementItem() {
    return Semantics(
      label: '用户协议',
      button: true,
      child: _buildSettingsItem(
        icon: Icons.description_outlined,
        title: '用户协议',
        subtitle: '查看服务条款和使用协议',
        iconColor: const Color(0xFF9B9A97),
        onTap: _showUserAgreement,
      ),
    );
  }

  /// 联系我们设置项
  Widget _buildContactUsItem() {
    return Semantics(
      label: '联系我们',
      button: true,
      child: _buildSettingsItem(
        icon: Icons.contact_support_outlined,
        title: '联系我们',
        subtitle: '反馈建议或获取帮助',
        iconColor: const Color(0xFF7C3AED),
        onTap: _contactUs,
      ),
    );
  }

  /// 评价应用设置项
  Widget _buildRateAppItem() {
    return Semantics(
      label: '评价应用',
      button: true,
      child: _buildSettingsItem(
        icon: Icons.star_outline,
        title: '评价应用',
        subtitle: '在应用商店为我们评分',
        iconColor: const Color(0xFFFFD700),
        onTap: _rateApp,
      ),
    );
  }

  /// 构建通用设置项
  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color iconColor,
    VoidCallback? onTap,
    bool showArrow = true,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: iconColor, size: 20),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        color: const Color(0xFF37352F).withValues(alpha: 0.6),
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),

              if (showArrow)
                Icon(
                  Icons.chevron_right,
                  color: const Color(0xFF9B9A97).withValues(alpha: 0.7),
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建开关设置项
  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color iconColor,
    required bool value,
    ValueChanged<bool>? onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: iconColor, size: 20),
          ),

          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Color(0xFF37352F),
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: const Color(0xFF37352F).withValues(alpha: 0.6),
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),

          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF2E7EED),
          ),
        ],
      ),
    );
  }

  /// 显示语言选择器
  void _showLanguageSelector() {
    final l10n = AppLocalizations.of(context)!;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.selectLanguage,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 20),
            _buildLanguageOption(
              l10n.chineseSimplified,
              const Locale('zh', 'CN'),
            ),
            _buildLanguageOption(l10n.english, const Locale('en', 'US')),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 构建语言选项
  Widget _buildLanguageOption(String name, Locale locale) {
    final currentLocale = ref.watch(localeProvider);
    final isSelected =
        currentLocale.languageCode == locale.languageCode &&
        currentLocale.countryCode == locale.countryCode;

    return ListTile(
      title: Text(
        name,
        style: const TextStyle(color: Color(0xFF37352F), fontSize: 16),
      ),
      trailing: isSelected
          ? const Icon(Icons.check, color: Color(0xFF2E7EED))
          : null,
      onTap: () async {
        // 使用 locale provider 切换语言
        await ref.read(localeProvider.notifier).changeLocale(locale);
        if (mounted) {
          Navigator.pop(context);
          _showRestartHint();
        }
      },
    );
  }

  /// 显示隐私政策
  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('隐私政策'),
        content: const SingleChildScrollView(
          child: Text(
            'OneDay应用隐私政策\n\n'
            '我们致力于保护您的隐私和个人信息安全。\n\n'
            '1. 信息收集\n'
            '我们仅收集为您提供服务所必需的信息。\n\n'
            '2. 信息使用\n'
            '您的数据仅用于改善学习体验。\n\n'
            '3. 信息共享\n'
            '我们不会与第三方分享您的个人信息。\n\n'
            '4. 数据安全\n'
            '我们采用业界标准的安全措施保护您的数据。\n\n'
            '如有疑问，请联系我们。',
            style: TextStyle(height: 1.5),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  /// 显示用户协议
  void _showUserAgreement() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('用户协议'),
        content: const SingleChildScrollView(
          child: Text(
            'OneDay应用用户协议\n\n'
            '欢迎使用OneDay学习应用！\n\n'
            '1. 服务内容\n'
            'OneDay为您提供学习时间管理、记忆训练等服务。\n\n'
            '2. 用户义务\n'
            '请合理使用应用功能，不得进行违法活动。\n\n'
            '3. 知识产权\n'
            '应用内容受知识产权法保护。\n\n'
            '4. 免责声明\n'
            '我们不对使用应用产生的结果承担责任。\n\n'
            '5. 协议变更\n'
            '我们保留修改本协议的权利。\n\n'
            '继续使用即表示同意本协议。',
            style: TextStyle(height: 1.5),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  /// 联系我们
  void _contactUs() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('联系我们'),
        content: const Text(
          '感谢您使用OneDay！\n\n'
          '如果您有任何问题或建议，请通过以下方式联系我们：\n\n'
          '📧 邮箱：<EMAIL>\n'
          '💬 微信：OneDay_Support\n'
          '📱 QQ群：123456789\n\n'
          '我们会尽快回复您的问题。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('好的'),
          ),
        ],
      ),
    );
  }

  /// 评价应用
  void _rateApp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('评价应用'),
        content: const Text(
          '喜欢OneDay吗？ ⭐\n\n'
          '您的评价是我们前进的动力！\n'
          '请前往应用商店为我们评分和留言。\n\n'
          '谢谢您的支持！ 🙏',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('稍后再说'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showComingSoon('应用商店跳转功能');
            },
            child: const Text('去评价'),
          ),
        ],
      ),
    );
  }

  /// 显示重启提示
  void _showRestartHint() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('设置已保存，部分功能需要重启应用生效'),
        backgroundColor: const Color(0xFF2E7EED),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: SnackBarAction(
          label: '知道了',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('该功能即将推出，敬请期待'),
        backgroundColor: const Color(0xFF2E7EED),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// 导出数据
  void _exportData() {
    _showComingSoon('数据导出');
  }

  /// 清理缓存
  void _clearCache() {
    _showComingSoon('缓存清理');
  }

  /// 重置数据
  void _resetData() {
    _showComingSoon('数据重置');
  }
}
